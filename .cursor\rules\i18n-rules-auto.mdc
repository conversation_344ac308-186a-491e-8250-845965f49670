---
description: 修改或添加任何涉及用户界面文本的代码时使用
globs: 
alwaysApply: false
---
# 国际化开发规范

## 使用场景
- 修改或添加任何涉及用户界面文本的代码时
- 处理翻译文件或国际化相关代码时

## 核心架构
- **主进程**: 使用 `mainI18n.t()` 处理菜单、窗口标题、系统通知
- **渲染进程**: 使用 `useTranslation()` 或 `useTranslationWithMarker()` 处理React组件
- **Ant Design集成**: 强制使用ConfigProvider配置Ant Design组件的国际化
- **文件结构**: 按命名空间组织 `src/locales/{lang}/{namespace}.json`
- **支持语言**: zh-CN, en, ru, fr

## 关键规则
- **绝对禁止硬编码**任何用户可见文本
- 主进程：`mainI18n.t('namespace.key', '默认值')`
- 渲染进程：`t('key', '默认值')` 配合 `useTranslation('namespace')`
- **推荐使用** `useTranslationWithMarker()` 实现自动文本元素标记
- 新功能必须同时更新所有语言文件
- 语言选择器文本必须从翻译文件的 `language.selector` 获取

## 文件结构和命名空间
```
src/locales/
├── zh-CN/          # 简体中文（默认）
│   ├── common.json # 通用按钮、标签 + 语言选择器文本
│   ├── menu.json   # 主进程菜单项
│   ├── dialog.json # 对话框文本
│   └── errors.json # 错误信息
├── en/             # 英文（回退语言）
├── ru/ └── fr/     # 俄文、法文
```

**命名规范**:
- app: `app.name`, `app.loading`
- common: `common.button.ok`, `common.language.selector.*`
- menu: `menu.file.label`, dialog: `dialog.confirm.title`
- errors: `errors.network`

## 语言选择器配置
每个语言的 `common.json` 必须包含：
```json
{
  "language": {
    "name": "当前语言的本地名称",
    "selector": {
      "title": "选择语言",
      "zh-CN": "简体中文",
      "en": "English",
      "ru": "Русский", 
      "fr": "Français"
    }
  }
}
```

## 使用方法

### 主进程
```typescript
import { mainI18n } from './i18n';

// 初始化
await mainI18n.initialize();

// 创建菜单
const template = [{
  label: mainI18n.t('menu.file.label', '文件'),
  submenu: [{ label: mainI18n.t('menu.file.new', '新建') }]
}];

// 切换语言
await mainI18n.changeLanguage('ru');
```

### 渲染进程（基础）
```typescript
import { useTranslation } from '../hooks/useTranslation';

function MyComponent() {
  const { t, changeLanguage } = useTranslation('common');

  return (
    <div>
      <h1>{t('button.save', '保存')}</h1>
      <button onClick={() => changeLanguage('en')}>
        {t('language.selector.en', 'English')}
      </button>
    </div>
  );
}
```

### 渲染进程（推荐 - 文本标记系统）
```typescript
import { useTranslationWithMarker } from '../hooks/useTranslationWithMarker';

function MyComponent() {
  const { t } = useTranslationWithMarker('common', {
    componentName: 'MyComponent',
    enableMarker: true
  });

  return (
    <button>
      {t('button.save', '保存', undefined, {
        elementType: 'button',
        isDynamic: false
      })}
    </button>
  );
}
```

## 重要注意事项

### 日志规范
- **开发日志**: 保持英文 `console.log('Language changed')`
- **用户错误**: 必须翻译 `t('errors.network', '网络错误')`

### 语言选择器
```typescript
// ✅ 正确：从翻译文件获取
const languageName = t(`language.selector.${langCode}`, '') || 
                     SUPPORTED_LANGUAGES[langCode].nativeName;

// ❌ 错误：硬编码语言名称
const languages = [{ code: 'zh-CN', name: '简体中文' }];
```

### 类型安全
- 语言代码: `'zh-CN' | 'en' | 'ru' | 'fr'`
- 命名空间: `TranslationNamespace` 类型约束
- 编译时检查翻译键名有效性

## 示例对比

### ✅ 正确示例
```typescript
// 主进程菜单
const menu = [{
  label: mainI18n.t('menu.file.label', '文件'),
  submenu: [{ 
    label: mainI18n.t('menu.file.new', '新建'),
    click: () => console.log('Menu clicked') // 日志英文
  }]
}];

// 渲染进程组件
const { t } = useTranslation('common');
const errorMsg = t('errors.save_failed', '保存失败');

// 语言切换
await changeLanguage('ru'); // 支持 zh-CN, en, ru, fr

// 动态内容
const msg = t('welcome', '欢迎 {{user}}！', { user: 'John' });
```

### ❌ 错误示例
```typescript
// 硬编码文本
alert('保存失败'); // 应使用翻译函数

// 硬编码语言选择器
<option value="zh-CN">简体中文</option> // 应从翻译文件获取

// 不支持的语言
await changeLanguage('ja'); // 应使用支持的语言

// 用户日志使用中文
console.log('设置已保存'); // 应使用英文
```

## 文本标记系统（推荐新组件使用）

### 核心功能
- 自动注册和管理文本元素
- 语言切换时批量更新
- 智能缓存和性能优化

### 元素类型
- `text`, `button`, `heading`, `tooltip`, `placeholder`, `aria-label`

### 配置选项
```typescript
const { t } = useTranslationWithMarker('common', {
  componentName: 'MyComponent',
  enableMarker: true,
  debug: process.env.NODE_ENV === 'development'
});
```

### Ant Design国际化集成

#### ConfigProvider + 语言切换
```typescript
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import enUS from 'antd/locale/en_US';
import ruRU from 'antd/locale/ru_RU';
import frFR from 'antd/locale/fr_FR';
import { useTranslation } from 'react-i18next';

const antdLocales = {
  'zh-CN': zhCN,
  'en': enUS, 
  'ru': ruRU,
  'fr': frFR
};

function App() {
  const { i18n } = useTranslation();
  const currentAntdLocale = antdLocales[i18n.language] || enUS;
  
  return (
    <ConfigProvider locale={currentAntdLocale}>
      {/* 应用内容 */}
    </ConfigProvider>
  );
}
```

#### Ant Design组件国际化
```typescript
// 正确：日期选择器自动跟随语言
import { DatePicker, Select } from 'antd';

function MyForm() {
  const { t } = useTranslation('common');
  
  return (
    <form>
      {/* DatePicker会自动使用ConfigProvider的locale */}
      <DatePicker placeholder={t('date.placeholder', '请选择日期')} />
      
      {/* Select组件的notFoundContent也会国际化 */}
      <Select placeholder={t('select.placeholder', '请选择')}>
        {/* 选项内容 */}
      </Select>
    </form>
  );
}
```

### 最佳实践
1. **Ant Design优先**: 所有UI组件必须使用Ant Design，确保国际化一致性
2. **ConfigProvider必须**: 根组件必须包装ConfigProvider处理Ant Design国际化
3. 新组件优先使用 `useTranslationWithMarker`
4. 静态文本设置 `isDynamic: false`
5. 指定正确的 `elementType`
6. 开发模式启用 `debug`
7. 生产环境禁用调试功能

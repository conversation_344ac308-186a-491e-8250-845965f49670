---
description: 当开发者输入"/检查"指令时使用
globs: 
alwaysApply: false
---
# 检查指令处理规范

## 使用场景
- 用户输入`/检查`指令时
- 需要进行全面代码质量检查时
- 排查项目中的潜在问题时

## 关键规则
- **Context7优先**：检查前必须使用Context7调研相关代码质量工具和检查标准
- 对当前代码进行细致的质量检查
- 根据检查发现的问题定位到具体模块文档
- 在对应模块文档的"错误追踪"部分记录问题
- 生成详细检查报告并按严重程度分类
- 提供具体的文件位置和修复建议

## 检查项目类型
- **🔴 严重问题**: 安全漏洞、功能性错误、数据丢失风险
- **🟡 警告问题**: 代码规范、性能优化、维护性问题
- **🔵 建议优化**: 代码重构、最佳实践、可读性改进

## 执行流程
1. **需求理解** → 2. **Context7调研** → 3. **全面代码检查** → 4. **问题分类** → 5. **定位相关模块** → 6. **更新错误追踪** → 7. **生成检查报告**

## 示例

<example>
```text
用户：/检查

AI回复：
🔍 开始全面代码质量检查...

🔍 Context7调研：
- 查询Electron项目代码质量检查标准
- 调研TypeScript代码规范最佳实践
- 查询安全漏洞检测工具和方法

📋 检查发现问题：

**🔴 严重问题 (2个)**
1. src/auth.js: 密码明文存储，存在安全风险
2. src/api.js: SQL注入漏洞，缺少参数验证

**🟡 警告问题 (3个)**
3. src/utils.js: 函数命名不符合camelCase规范
4. config/app.js: 硬编码API地址，应使用环境变量
5. src/components/: 缺少错误边界处理

🔄 正在更新模块文档...
- ✅ 已在 docs/auth.md 中添加 ERR-AUTH-20250602-01
- ✅ 已在 docs/backend-api.md 中添加 ERR-API-20250602-01

检查完成！建议优先处理严重问题。
```
</example>

<example type="invalid">
```text
"代码看起来没什么问题。"
（错误：没有Context7调研，没有详细检查，没有具体分析，没有更新文档）
```

</example>
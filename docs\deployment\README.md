# 部署文档

## 📖 概述

QuickStart项目部署文档，包含构建、打包、分发和安装的完整流程，支持Windows、macOS和Linux多平台部署。

## ✅ 实现状态

**完成度**: 90% ✅  
**最后验证**: 2025-07-02  
**状态**: 基本完成，支持开发和生产环境

## 🏗️ 构建系统

### 技术栈
- **electron-builder**: 应用打包和分发
- **Webpack**: 前端资源打包
- **TypeScript**: 代码编译
- **Node.js**: 构建环境

### 构建脚本
```json
{
  "scripts": {
    "dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main\"",
    "dev:renderer": "webpack serve --config webpack.renderer.config.js",
    "dev:main": "webpack --config webpack.main.config.js --watch",
    "build": "npm run build:renderer && npm run build:main",
    "build:renderer": "webpack --config webpack.renderer.config.js --mode production",
    "build:main": "webpack --config webpack.main.config.js --mode production",
    "pack": "electron-builder --dir",
    "dist": "electron-builder",
    "dist:win": "electron-builder --win",
    "dist:mac": "electron-builder --mac",
    "dist:linux": "electron-builder --linux"
  }
}
```

## 📦 打包配置

### electron-builder配置
```json
{
  "build": {
    "appId": "com.quickstart.app",
    "productName": "QuickStart",
    "directories": {
      "output": "dist",
      "buildResources": "build"
    },
    "files": [
      "build/**/*",
      "node_modules/**/*",
      "package.json"
    ],
    "extraResources": [
      {
        "from": "resources",
        "to": "resources",
        "filter": ["**/*"]
      }
    ],
    "win": {
      "target": [
        {
          "target": "nsis",
          "arch": ["x64", "ia32"]
        },
        {
          "target": "portable",
          "arch": ["x64"]
        }
      ],
      "icon": "build/icon.ico",
      "requestedExecutionLevel": "asInvoker"
    },
    "mac": {
      "target": [
        {
          "target": "dmg",
          "arch": ["x64", "arm64"]
        },
        {
          "target": "zip",
          "arch": ["x64", "arm64"]
        }
      ],
      "icon": "build/icon.icns",
      "category": "public.app-category.productivity"
    },
    "linux": {
      "target": [
        {
          "target": "AppImage",
          "arch": ["x64"]
        },
        {
          "target": "deb",
          "arch": ["x64"]
        }
      ],
      "icon": "build/icon.png",
      "category": "Utility"
    },
    "nsis": {
      "oneClick": false,
      "allowToChangeInstallationDirectory": true,
      "createDesktopShortcut": true,
      "createStartMenuShortcut": true,
      "shortcutName": "QuickStart"
    },
    "dmg": {
      "title": "QuickStart ${version}",
      "backgroundColor": "#ffffff",
      "window": {
        "width": 540,
        "height": 380
      },
      "contents": [
        {
          "x": 410,
          "y": 190,
          "type": "link",
          "path": "/Applications"
        },
        {
          "x": 130,
          "y": 190,
          "type": "file"
        }
      ]
    }
  }
}
```

## 🖼️ 应用图标

### 图标规格
```
build/
├── icon.ico          # Windows图标 (256x256)
├── icon.icns         # macOS图标 (512x512)
├── icon.png          # Linux图标 (512x512)
└── icons/            # 多尺寸图标
    ├── 16x16.png
    ├── 32x32.png
    ├── 48x48.png
    ├── 64x64.png
    ├── 128x128.png
    ├── 256x256.png
    └── 512x512.png
```

### 图标生成脚本
```bash
# 从SVG生成多尺寸PNG
npm install -g svg2png-cli
svg2png icon.svg --output build/icons/ --sizes 16,32,48,64,128,256,512

# 生成Windows ICO
npm install -g png-to-ico
png-to-ico build/icons/256x256.png build/icon.ico

# 生成macOS ICNS (需要macOS)
iconutil -c icns build/icons/ -o build/icon.icns
```

## 🚀 部署流程

### 1. 开发环境部署 ✅
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 热重载开发
npm run dev:renderer  # 前端热重载
npm run dev:main      # 主进程监听
```

### 2. 生产构建 ✅
```bash
# 清理构建目录
npm run clean

# 构建应用
npm run build

# 验证构建
npm run start
```

### 3. 应用打包 ✅
```bash
# 打包但不分发 (用于测试)
npm run pack

# 完整分发包
npm run dist

# 平台特定打包
npm run dist:win     # Windows
npm run dist:mac     # macOS  
npm run dist:linux   # Linux
```

## 📋 构建产物

### Windows平台
```
dist/
├── QuickStart Setup 1.0.0.exe     # NSIS安装包
├── QuickStart 1.0.0.exe           # 便携版
└── win-unpacked/                   # 解压版
    ├── QuickStart.exe
    ├── resources/
    └── locales/
```

### macOS平台
```
dist/
├── QuickStart-1.0.0.dmg           # DMG安装包
├── QuickStart-1.0.0-mac.zip       # ZIP压缩包
└── mac/                           # 应用包
    └── QuickStart.app/
        ├── Contents/
        │   ├── MacOS/
        │   ├── Resources/
        │   └── Info.plist
        └── ...
```

### Linux平台
```
dist/
├── QuickStart-1.0.0.AppImage       # AppImage包
├── quickstart_1.0.0_amd64.deb     # Debian包
└── linux-unpacked/                 # 解压版
    ├── quickstart
    ├── resources/
    └── locales/
```

## 🔧 环境配置

### 开发环境要求
- **Node.js**: 18.x 或更高版本
- **npm**: 8.x 或更高版本
- **Python**: 3.x (用于native模块编译)
- **Git**: 版本控制

### 构建环境要求

#### Windows
```bash
# 安装Windows构建工具
npm install -g windows-build-tools

# 或使用Visual Studio Build Tools
# 下载并安装 Visual Studio Build Tools 2019
```

#### macOS
```bash
# 安装Xcode命令行工具
xcode-select --install

# 安装Homebrew (可选)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

#### Linux
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install build-essential libnss3-dev libatk-bridge2.0-dev libdrm2-dev libxcomposite-dev libxdamage-dev libxrandr-dev libgbm-dev libxss-dev libasound2-dev

# CentOS/RHEL
sudo yum groupinstall "Development Tools"
sudo yum install nss-devel atk-devel at-spi2-atk-devel libdrm-devel libXcomposite-devel libXdamage-devel libXrandr-devel mesa-libgbm-devel libXScrnSaver-devel alsa-lib-devel
```

## 📊 构建性能

### 构建时间
- **开发构建**: ~30秒
- **生产构建**: ~2分钟
- **完整打包**: ~5分钟

### 包大小
- **Windows**: ~150MB (安装包 ~50MB)
- **macOS**: ~160MB (DMG ~55MB)
- **Linux**: ~140MB (AppImage ~45MB)

### 优化策略
```javascript
// webpack优化配置
module.exports = {
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
          },
        },
      }),
    ],
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    },
  },
};
```

## 🔍 质量保证

### 构建验证
```bash
# 构建前检查
npm run lint          # 代码规范检查
npm run type-check    # TypeScript类型检查
npm run test          # 单元测试

# 构建后验证
npm run test:e2e      # 端到端测试
npm run security-scan # 安全扫描
```

### 自动化测试
```yaml
# GitHub Actions示例
name: Build and Test
on: [push, pull_request]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - run: npm ci
    - run: npm run build
    - run: npm run test
    - run: npm run pack
```

## 🚀 发布流程

### 版本管理
```bash
# 更新版本号
npm version patch    # 1.0.0 -> 1.0.1
npm version minor    # 1.0.0 -> 1.1.0
npm version major    # 1.0.0 -> 2.0.0

# 创建发布标签
git tag v1.0.0
git push origin v1.0.0
```

### 自动发布
```yaml
# 发布工作流
name: Release
on:
  push:
    tags:
      - 'v*'

jobs:
  release:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-node@v3
    - run: npm ci
    - run: npm run dist
    - uses: softprops/action-gh-release@v1
      with:
        files: dist/*
```

## 🔍 已知问题

### 构建问题 ⚠️
1. **native模块编译**: 某些依赖需要编译环境
2. **代码签名**: macOS和Windows需要开发者证书
3. **自动更新**: 需要配置更新服务器

### 解决方案
```bash
# 预编译native模块
npm run rebuild

# 跳过代码签名 (开发环境)
export CSC_IDENTITY_AUTO_DISCOVERY=false

# 禁用自动更新检查
export DISABLE_AUTO_UPDATE=true
```

## 📈 部署监控

### 构建指标
- **成功率**: 95%+
- **构建时间**: <5分钟
- **包大小**: <200MB
- **启动时间**: <3秒

### 监控脚本
```bash
#!/bin/bash
# 构建监控脚本
start_time=$(date +%s)

npm run build
build_status=$?

end_time=$(date +%s)
duration=$((end_time - start_time))

echo "构建状态: $build_status"
echo "构建耗时: ${duration}秒"

if [ $build_status -eq 0 ]; then
  echo "✅ 构建成功"
else
  echo "❌ 构建失败"
  exit 1
fi
```

## 🔗 相关文档

- [后端文档](../backend/README.md)
- [前端文档](../frontend/README.md)
- [配置管理文档](../configuration/README.md)
- [错误追踪](../error-tracking.md)

---

*最后更新: 2025-07-02*  
*文档版本: 1.0*

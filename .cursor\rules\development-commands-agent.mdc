---
description: 当开发者输入"/开发"时使用
globs: 
alwaysApply: false
---
# 开发指令处理规范

## 使用场景
- 用户输入`/开发`指令时
- 需要进行功能开发时
- 批量开发多个模块时

## 关键规则
- **Context7优先**：开发前必须使用Context7调研相关技术和组件用法
- 理解开发需求并制定详细方案
- 获得用户确认后开始开发
- 严格执行备份程序保护现有代码
- 按模块化方式组织开发任务
- 开发完成后立即更新相关文档

## 开发流程
1. **需求理解** → 2. **Context7调研** → 3. **方案设计** → 4. **用户确认** → 5. **备份执行** → 6. **功能开发** → 7. **测试验证** → 8. **文档更新**

## 开发类型
- **新功能开发**: 添加全新的功能模块
- **功能增强**: 在现有功能基础上扩展
- **重构优化**: 改进代码结构和性能
- **Bug修复**: 修复现有功能问题

## 示例

<example>
```text
用户：/开发 添加用户头像上传功能

AI回复：
🚀 开发计划：用户头像上传功能

🔍 Context7调研：
- 查询Ant Design Upload组件最佳实践
- 调研Electron文件处理API
- 查询Node.js图片处理库用法
- 查询Ant Design文件上传的主题定制方法

📋 功能分析：
- 前端：Ant Design Upload组件 + React Hook
- 主进程：IPC处理 + Node.js文件操作
- 数据库：Prisma schema + SQLite
- 存储：本地文件系统管理
- UI集成：Ant Design ConfigProvider主题适配

💡 技术方案：
1. 使用Ant Design Upload组件实现文件上传
2. 通过IPC发送文件数据到主进程
3. 主进程使用Node.js处理图片压缩和格式转换
4. 使用Prisma更新用户头像字段
5. 集成Ant Design主题系统确保UI一致性

✅ 方案确认后开始开发...

🔄 执行备份：
- ✅ 备份src/renderer/components/UserProfile.tsx -> copy/UserProfile.tsx
- ✅ 备份src/main/ipc/user.ts -> copy/user.ts

🔧 开发进度：
- ✅ React头像上传组件开发完成
- ✅ 主进程IPC文件处理完成
- ✅ Prisma数据模型更新完成
- ✅ 图片处理功能完成

📝 文档更新：
- ✅ 更新docs/components/components.md
- ✅ 更新docs/api/api.md
- ✅ 更新docs/database/database.md

开发完成！用户头像上传功能已就绪。
```
</example>

<example type="invalid">
```text
"开始开发..."然后直接修改代码
（错误：没有Context7调研，没有方案设计，没有用户确认，没有备份，没有文档更新）
```

</example>
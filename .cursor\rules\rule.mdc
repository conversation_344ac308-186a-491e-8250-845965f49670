---
description: 需要创建新的Cursor规则时使用
globs: 
alwaysApply: false
---
# Cursor Rule 规则格式规范

## 使用场景
- 需要创建新的Cursor规则时
- 学习规则文件格式时
- 规范化规则编写时

## 关键规则
- 规则文件必须位于`.cursor/rules/`目录
- 文件扩展名必须为`.mdc`
- 前置信息包含：description、globs、alwaysApply
- 正文包含：使用场景、关键规则、示例
- 规则类型后缀：`-auto|-agent|-manual|-always`

## 规则文件模板结构

```mdc
---
description: `根据规则内容，生成包含关键场景、动作、触发条件、结果、格式的内容，限制在 150 字以内`
globs: 空白或模式 (例如: *.js, *.ts, *.py, .vscode/*.json, .cursor/**/*.mdc)
alwaysApply: {true 或 false}
---

# 规则标题

## 使用场景
- 何时应用此规则
- 前提条件或要求

## 关键规则
- 简洁的、列表形式的行动规则，模型必须遵循
- 始终执行 X
- 绝不执行 Y

## 示例
<example>
好的简洁示例及其说明
</example>

<example type="invalid">
错误的简洁示例及其说明
</example>
```

## 规则类型说明
- **Auto规则**: 自动应用于匹配glob模式的文件
- **Agent规则**: 处理特定指令或场景的专用规则
- **Manual规则**: 需要手动激活的规则
- **Always规则**: 全局应用于所有对话的规则

## Glob模式示例
- 语言文件：`*.js *.ts *.py *.cpp`
- 测试文件：`*.test.js *.test.ts`
- 文档文件：`docs/**/*.md *.md`
- 配置文件：`*.config.{js,json} *.json`
- 前端组件：`frontend/**/*.{js,jsx,ts,tsx}`

## 示例

<example>
```mdc
---
description: 处理JavaScript文件时确保代码质量和规范
globs: *.js *.jsx
alwaysApply: false
---

# JavaScript代码规范

## 使用场景
- 编辑JavaScript文件时
- 代码审查时

## 关键规则
- 使用ES6+语法
- 函数必须有注释
- 避免使用var，优先使用const

## 示例
<example>
// 正确的函数定义
const calculateSum = (a, b) => {
  return a + b;
};
</example>
```
</example>

<example type="invalid">
直接编写规则内容而不遵循模板格式

</example>
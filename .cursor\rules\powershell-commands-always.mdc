---
description: 在需要使用PowerShell终端执行命令时使用
globs: 
alwaysApply: false
---
# PowerShell 使用规范

## 使用场景
- Windows环境下执行终端命令
- 文件和目录操作
- 系统管理任务
- 自动化脚本执行

## 命令格式
```powershell
powershell -Command "具体命令"
```

## 关键规则

### 路径处理
- 所有路径用单引号保护：`'C:\path\to\file'`
- 使用Path类方法：`[System.IO.Path]::Combine('C:\temp', 'file.txt')`
- 条件检查：`if (Test-Path 'file.txt') { ... }`

### 文件操作
```powershell
# 创建目录
powershell -Command "New-Item -Path 'backup\data' -ItemType Directory -Force"

# 复制文件
powershell -Command "Copy-Item -Path 'src\app.js' -Destination 'backup\app.js'"

# 备份文件（带时间戳）
powershell -Command "Copy-Item 'config.json' ('backup\config_' + (Get-Date -Format 'yyyyMMdd') + '.json')"
```

### 错误处理
```powershell
# 静默错误
powershell -Command "Get-Process -Name 'nonexistent' -ErrorAction SilentlyContinue"

# Try-Catch结构
powershell -Command "try { Copy-Item 'source' 'dest' } catch { Write-Error $_.Exception.Message }"
```

### 对象导向操作
```powershell
# 使用管道处理对象
powershell -Command "Get-Process | Where-Object CPU -gt 100 | Select-Object Name, CPU"

# 服务管理
powershell -Command "Get-Service | Where-Object Status -eq 'Running'"

# JSON处理
powershell -Command "Get-Content 'data.json' | ConvertFrom-Json | Where-Object status -eq 'active'"
```

### 系统信息
```powershell
# 磁盘空间
powershell -Command "Get-CimInstance Win32_LogicalDisk | Select-Object DeviceID, @{n='FreeGB';e={[math]::Round($_.FreeSpace/1GB,2)}}"

# 内存使用
powershell -Command "Get-CimInstance Win32_OperatingSystem | Select-Object @{n='FreeGB';e={[math]::Round($_.FreePhysicalMemory/1MB,2)}}"

# 事件日志
powershell -Command "Get-WinEvent -LogName System -MaxEvents 10"
```

## 避免使用
- 过时的WMI cmdlets（使用Get-CimInstance代替Get-WmiObject）
- 字符串拼接路径（使用Path.Combine）
- 长命令（拆分为多个简短命令）
- 不安全的路径引用（必须用单引号）

## 最佳实践
- 优先使用现代cmdlets
- 利用对象管道而非文本处理
- 添加适当的错误处理
- 使用-Force参数避免交互提示
- 为重要操作添加-Verbose参数
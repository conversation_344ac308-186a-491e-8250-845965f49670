{"title": "Gestion des fichiers", "list": {"empty": "La liste des fichiers est vide", "loading": "Chargement de la liste des fichiers...", "refreshing": "Actualisation...", "total": "Total {{count}} éléments", "selected": "{{count}} éléments sélectionnés", "filtered": "Affichage {{count}} / {{total}} éléments"}, "actions": {"add": "Ajouter un fichier", "addFile": "Ajouter un fichier", "addFolder": "Ajouter un dossier", "remove": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openWith": "Ouvrir avec", "openFolder": "<PERSON><PERSON><PERSON><PERSON><PERSON> le dossier", "showInExplorer": "Afficher dans l'explorateur", "copyPath": "<PERSON><PERSON><PERSON> le chemin", "rename": "<PERSON>mmer", "properties": "Propriétés", "refresh": "Actualiser", "selectAll": "<PERSON><PERSON>", "clearSelection": "Effacer la sélection", "clearList": "Vider la liste", "runAsAdmin": "Exécuter en tant qu'administrateur", "addToFavorites": "Ajouter aux favoris", "removeFromFavorites": "Retirer des favoris", "pin": "<PERSON><PERSON><PERSON>", "unpin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "move": "<PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "cut": "Couper", "paste": "<PERSON><PERSON>"}, "sort": {"title": "Méthode de tri", "name": "Par nom", "date": "Par date", "size": "Par taille", "type": "Par type", "frequency": "Par fréquence", "recent": "Par utilisation récente", "ascending": "Croissant", "descending": "Décroissant"}, "view": {"title": "Mode d'affichage", "list": "<PERSON><PERSON> liste", "grid": "Vue grille", "compact": "Vue compacte", "details": "<PERSON><PERSON>", "tiles": "<PERSON>ue mosaï<PERSON>", "icons": "Vue icônes"}, "filter": {"title": "<PERSON><PERSON><PERSON>", "all": "<PERSON>ut", "files": "Fichiers", "folders": "Dossiers", "favorites": "<PERSON><PERSON><PERSON>", "recent": "<PERSON><PERSON><PERSON>", "pinned": "<PERSON><PERSON><PERSON>", "type": "Type de fichier", "size": "<PERSON><PERSON>", "date": "Date de modification"}, "search": {"placeholder": "Rechercher des fichiers...", "noResults": "<PERSON><PERSON><PERSON> fichier correspondant trouvé", "searching": "Recherche en cours...", "results": "{{count}} résultats trouvés", "clear": "Effacer la recherche"}, "properties": {"title": "Propriétés du fichier", "name": "Nom", "path": "Chemin", "size": "<PERSON><PERSON>", "type": "Type", "created": "<PERSON><PERSON><PERSON>", "modified": "<PERSON><PERSON><PERSON><PERSON>", "accessed": "Accédé", "permissions": "Permissions", "attributes": "Attributs", "version": "Version", "description": "Description", "company": "Société", "copyright": "Copyright", "launchCount": "Nombre de lancements", "lastLaunched": "Dernier lancement", "addedDate": "Date d'ajout", "tags": "Étiquettes", "notes": "Notes"}, "status": {"exists": "Le fichier existe", "notExists": "Le fichier n'existe pas", "accessible": "Accessible", "notAccessible": "Non accessible", "executable": "Exécutable", "notExecutable": "Non exécutable", "readable": "Lisible", "writable": "Modifiable", "hidden": "Caché", "system": "Fichier système", "archive": "Archive", "compressed": "<PERSON><PERSON><PERSON><PERSON>", "encrypted": "<PERSON><PERSON><PERSON>", "symlink": "Lien symbolique", "shortcut": "<PERSON><PERSON><PERSON><PERSON>"}, "types": {"file": "<PERSON><PERSON><PERSON>", "folder": "Dossier", "executable": "Fichier exécutable", "document": "Document", "image": "Image", "video": "Vidéo", "audio": "Audio", "archive": "Archive", "code": "Fichier de code", "data": "<PERSON><PERSON><PERSON>", "system": "Fichier système", "unknown": "Type inconnu"}, "messages": {"loadFailed": "Échec du chargement de la liste des fichiers", "addFileSuccess": "<PERSON><PERSON><PERSON> : {{name}}", "addFileFailed": "Échec de l'ajout du fichier", "addFolderSuccess": "Do<PERSON><PERSON> a<PERSON> : {{name}}", "addFolderFailed": "Échec de l'ajout du dossier", "launchSuccess": "Lancé : {{name}}", "launchFailed": "Échec du lancement : {{name}}", "launchError": "Échec du lancement du fichier", "removeSuccess": "Fichier supprimé de la liste", "removeFailed": "Échec de la suppression du fichier", "removeError": "Échec de la suppression du fichier", "dragFilesSuccess": "{{count}} fi<PERSON><PERSON> a<PERSON>s", "dragFilesFailed": "Échec de l'ajout des fichiers", "dragFilesError": "Échec du traitement des fichiers glissés", "dragFilesWarning": "Impossible d'obtenir le chemin du fichier, veuillez utiliser le bouton d'ajout", "fileAdded": "<PERSON><PERSON><PERSON>", "filesAdded": "{{count}} fi<PERSON><PERSON> a<PERSON>s", "fileRemoved": "<PERSON><PERSON><PERSON> supprimé", "filesRemoved": "{{count}} fichiers supprimés", "fileOpened": "<PERSON><PERSON><PERSON> ouvert", "fileNotFound": "Fichier non trouvé", "accessDenied": "<PERSON><PERSON>ès refusé", "operationFailed": "Opération échouée", "confirmRemove": "Confirmer la suppression des fichiers sélectionnés ?", "confirmDelete": "Confirmer la suppression des fichiers sélectionnés ? Cette opération ne peut pas être annulée !", "confirmClearList": "Confirmer la suppression de la liste des fichiers ?", "dragDropHint": "C<PERSON>z ou glissez des fichiers dans cette zone pour les ajouter à la liste de lancement rapide", "dragDropSupport": "Prise en charge de l'ajout simple ou par lots de fichiers et dossiers", "noFileSelected": "<PERSON><PERSON><PERSON> fichier s<PERSON>", "invalidPath": "Chem<PERSON> invalide", "pathTooLong": "Chemin trop long", "nameRequired": "Veuillez saisir le nom du fichier", "nameInvalid": "Nom de fichier invalide", "alreadyExists": "Le fichier existe déjà", "copySuccess": "Chemin copié dans le presse-papiers", "launchTooltip": "Lancer", "deleteTooltip": "<PERSON><PERSON><PERSON><PERSON>"}, "ui": {"title": "Liste des fichiers", "description": "<PERSON><PERSON><PERSON> vos fichiers et dossiers de lancement rapide", "modifiedTime": "Modifié : {{date}}", "fileSize": "Taille : {{size}}", "addedTime": "Ajouté : {{date}}", "launchCount": "Nombre de lancements : {{count}}", "lastLaunched": "Dernier lancement : {{date}}"}}
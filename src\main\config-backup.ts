/**
 * 配置备份和恢复工具
 * 提供配置文件的版本管理、备份和恢复功能
 */

import * as fs from 'fs';
import * as path from 'path';
import { app } from 'electron';
import log from 'electron-log';

// 备份元数据接口
interface BackupMetadata {
  version: string;
  timestamp: number;
  configType: string;
  checksum: string;
  description?: string;
}

// 备份文件信息
export interface BackupInfo {
  filename: string;
  metadata: BackupMetadata;
  filePath: string;
  size: number;
}

/**
 * 配置备份管理器
 */
export class ConfigBackupManager {
  private backupDir: string = '';
  private maxBackups: number;
  private isInitialized: boolean = false;

  constructor(backupDir?: string, maxBackups: number = 10) {
    this.maxBackups = maxBackups;
    if (backupDir) {
      this.backupDir = backupDir;
      this.isInitialized = true;
      this.ensureBackupDirectory();
    }
    // 如果没有提供backupDir，延迟初始化
  }

  /**
   * 初始化备份目录（在app ready后调用）
   */
  public initialize(): void {
    if (!this.isInitialized) {
      this.backupDir = path.join(app.getPath('appData'), 'QuickStartAPP', 'backups');
      this.isInitialized = true;
      this.ensureBackupDirectory();
    }
  }

  /**
   * 确保备份目录存在
   */
  private ensureBackupDirectory(): void {
    if (this.backupDir && !fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
  }

  /**
   * 检查是否已初始化
   */
  private checkInitialized(): void {
    if (!this.isInitialized) {
      throw new Error('ConfigBackupManager not initialized. Call initialize() first.');
    }
  }

  /**
   * 计算文件校验和
   */
  private calculateChecksum(filePath: string): string {
    try {
      if (!fs.existsSync(filePath)) {
        log.warn(`File not found for checksum calculation: ${filePath}`);
        return '';
      }

      const content = fs.readFileSync(filePath, 'utf-8');

      // 如果文件为空，返回空字符串的哈希值
      if (!content.trim()) {
        log.warn(`Empty file for checksum calculation: ${filePath}`);
        const crypto = require('crypto');
        return crypto.createHash('sha256').update('').digest('hex');
      }

      const crypto = require('crypto');
      return crypto.createHash('sha256').update(content).digest('hex');
    } catch (error) {
      log.error('Failed to calculate checksum:', error);
      return '';
    }
  }

  /**
   * 生成备份文件名
   */
  private generateBackupFilename(configType: string, timestamp: number): string {
    const date = new Date(timestamp);
    const dateStr = date.toISOString().replace(/[:.]/g, '-');
    return `${configType}_${dateStr}.backup`;
  }

  /**
   * 验证JSON文件格式
   */
  private validateJsonFile(filePath: string): { isValid: boolean; content?: unknown; error?: string } {
    try {
      if (!fs.existsSync(filePath)) {
        return { isValid: false, error: 'File does not exist' };
      }

      const content = fs.readFileSync(filePath, 'utf-8').trim();

      // 检查文件是否为空
      if (!content) {
        return { isValid: false, error: 'File is empty' };
      }

      // 尝试解析JSON
      const parsed = JSON.parse(content);
      return { isValid: true, content: parsed };
    } catch (error) {
      return { isValid: false, error: `JSON parse error: ${(error as Error).message}` };
    }
  }

  /**
   * 创建配置备份
   */
  async createBackup(
    configFilePath: string,
    configType: string,
    description?: string
  ): Promise<string | null> {
    try {
      this.checkInitialized();

      if (!fs.existsSync(configFilePath)) {
        log.warn(`Config file not found: ${configFilePath}`);
        return null;
      }

      // 验证配置文件格式
      const validation = this.validateJsonFile(configFilePath);
      if (!validation.isValid) {
        log.warn(`Skipping backup for invalid config file: ${configFilePath}, reason: ${validation.error}`);
        return null;
      }

      const timestamp = Date.now();
      const checksum = this.calculateChecksum(configFilePath);
      const backupFilename = this.generateBackupFilename(configType, timestamp);
      const backupFilePath = path.join(this.backupDir, backupFilename);

      // 创建备份元数据
      const metadata: BackupMetadata = {
        version: '1.0.0',
        timestamp,
        configType,
        checksum,
        description,
      };

      // 创建备份文件内容（包含元数据和配置数据）
      const backupContent = {
        metadata,
        config: validation.content, // 使用已验证的内容
      };

      // 写入备份文件
      fs.writeFileSync(backupFilePath, JSON.stringify(backupContent, null, 2), 'utf-8');

      // 只在开发模式下输出详细的备份日志
      if (process.env.NODE_ENV === 'development') {
        log.info(`Config backup created: ${backupFilename}`);
      }

      // 清理旧备份
      await this.cleanupOldBackups(configType);

      return backupFilePath;
    } catch (error) {
      log.error('Failed to create backup:', error);
      return null;
    }
  }

  /**
   * 恢复配置从备份
   */
  async restoreFromBackup(
    backupFilePath: string,
    targetConfigPath: string
  ): Promise<boolean> {
    try {
      if (!fs.existsSync(backupFilePath)) {
        log.error(`Backup file not found: ${backupFilePath}`);
        return false;
      }

      // 读取备份文件
      const backupContent = fs.readFileSync(backupFilePath, 'utf-8').trim();

      if (!backupContent) {
        log.error('Backup file is empty');
        return false;
      }

      let backup;
      try {
        backup = JSON.parse(backupContent);
      } catch (parseError) {
        log.error('Failed to parse backup file JSON:', parseError);
        return false;
      }

      if (!backup.metadata || !backup.config) {
        log.error('Invalid backup file format - missing metadata or config');
        return false;
      }

      // 验证备份完整性
      if (!this.validateBackup(backup)) {
        log.error('Backup validation failed');
        return false;
      }

      // 创建当前配置的备份（作为恢复前的安全措施）
      if (fs.existsSync(targetConfigPath)) {
        const configType = backup.metadata.configType;
        await this.createBackup(targetConfigPath, `${configType}_pre_restore`, 'Pre-restore backup');
      }

      // 恢复配置
      const configContent = JSON.stringify(backup.config, null, 2);
      fs.writeFileSync(targetConfigPath, configContent, 'utf-8');

      log.info(`Config restored from backup: ${path.basename(backupFilePath)}`);
      return true;
    } catch (error) {
      log.error('Failed to restore from backup:', error);
      return false;
    }
  }

  /**
   * 验证备份文件
   */
  private validateBackup(backup: unknown): boolean {
    try {
      // 类型守卫检查
      if (typeof backup !== 'object' || backup === null) {
        return false;
      }

      const backupObj = backup as Record<string, unknown>;
      const { metadata, config } = backupObj;

      // 检查必要字段
      if (!metadata || !config) {
        return false;
      }

      // 验证metadata类型
      if (typeof metadata !== 'object' || metadata === null) {
        return false;
      }

      const metadataObj = metadata as Record<string, unknown>;
      if (!metadataObj.version || !metadataObj.timestamp || !metadataObj.configType) {
        return false;
      }

      // 验证配置数据格式
      if (typeof config !== 'object') {
        return false;
      }

      return true;
    } catch (error) {
      log.error('Backup validation error:', error);
      return false;
    }
  }

  /**
   * 获取备份列表
   */
  async getBackupList(configType?: string): Promise<BackupInfo[]> {
    try {
      const files = fs.readdirSync(this.backupDir);
      const backups: BackupInfo[] = [];

      for (const filename of files) {
        if (!filename.endsWith('.backup')) {
          continue;
        }

        const filePath = path.join(this.backupDir, filename);
        
        try {
          const content = fs.readFileSync(filePath, 'utf-8').trim();

          // 跳过空文件
          if (!content) {
            log.warn(`Skipping empty backup file: ${filename}`);
            continue;
          }

          const backup = JSON.parse(content);

          if (backup.metadata) {
            // 如果指定了配置类型，则过滤
            if (configType && backup.metadata.configType !== configType) {
              continue;
            }

            const stats = fs.statSync(filePath);
            backups.push({
              filename,
              metadata: backup.metadata,
              filePath,
              size: stats.size,
            });
          } else {
            log.warn(`Invalid backup file format (missing metadata): ${filename}`);
          }
        } catch (error) {
          log.warn(`Failed to read backup file ${filename}:`, error);
        }
      }

      // 按时间戳降序排序（最新的在前）
      return backups.sort((a, b) => b.metadata.timestamp - a.metadata.timestamp);
    } catch (error) {
      log.error('Failed to get backup list:', error);
      return [];
    }
  }

  /**
   * 清理旧备份
   */
  async cleanupOldBackups(configType: string): Promise<void> {
    try {
      const backups = await this.getBackupList(configType);
      
      if (backups.length <= this.maxBackups) {
        return;
      }

      // 删除超出限制的旧备份
      const backupsToDelete = backups.slice(this.maxBackups);
      
      for (const backup of backupsToDelete) {
        try {
          fs.unlinkSync(backup.filePath);
          // 只在开发模式下输出详细的备份清理日志
          if (process.env.NODE_ENV === 'development') {
            log.info(`Deleted old backup: ${backup.filename}`);
          }
        } catch (error) {
          log.warn(`Failed to delete backup ${backup.filename}:`, error);
        }
      }
    } catch (error) {
      log.error('Failed to cleanup old backups:', error);
    }
  }

  /**
   * 删除备份
   */
  async deleteBackup(backupFilePath: string): Promise<boolean> {
    try {
      if (fs.existsSync(backupFilePath)) {
        fs.unlinkSync(backupFilePath);
        log.info(`Backup deleted: ${path.basename(backupFilePath)}`);
        return true;
      }
      return false;
    } catch (error) {
      log.error('Failed to delete backup:', error);
      return false;
    }
  }

  /**
   * 获取备份统计信息
   */
  async getBackupStats(): Promise<{
    totalBackups: number;
    totalSize: number;
    configTypes: string[];
    oldestBackup?: number;
    newestBackup?: number;
  }> {
    try {
      const backups = await this.getBackupList();
      
      const configTypes = [...new Set(backups.map(b => b.metadata.configType))];
      const totalSize = backups.reduce((sum, b) => sum + b.size, 0);
      
      const timestamps = backups.map(b => b.metadata.timestamp);
      const oldestBackup = timestamps.length > 0 ? Math.min(...timestamps) : undefined;
      const newestBackup = timestamps.length > 0 ? Math.max(...timestamps) : undefined;

      return {
        totalBackups: backups.length,
        totalSize,
        configTypes,
        oldestBackup,
        newestBackup,
      };
    } catch (error) {
      log.error('Failed to get backup stats:', error);
      return {
        totalBackups: 0,
        totalSize: 0,
        configTypes: [],
      };
    }
  }

  /**
   * 导出备份到指定目录
   */
  async exportBackup(backupFilePath: string, exportDir: string): Promise<boolean> {
    try {
      if (!fs.existsSync(backupFilePath)) {
        return false;
      }

      if (!fs.existsSync(exportDir)) {
        fs.mkdirSync(exportDir, { recursive: true });
      }

      const filename = path.basename(backupFilePath);
      const exportPath = path.join(exportDir, filename);
      
      fs.copyFileSync(backupFilePath, exportPath);
      log.info(`Backup exported to: ${exportPath}`);
      
      return true;
    } catch (error) {
      log.error('Failed to export backup:', error);
      return false;
    }
  }

  /**
   * 导入备份从外部文件
   */
  async importBackup(externalBackupPath: string): Promise<boolean> {
    try {
      if (!fs.existsSync(externalBackupPath)) {
        return false;
      }

      // 验证备份文件格式
      const content = fs.readFileSync(externalBackupPath, 'utf-8').trim();

      if (!content) {
        log.error('External backup file is empty');
        return false;
      }

      let backup;
      try {
        backup = JSON.parse(content);
      } catch (parseError) {
        log.error('Failed to parse external backup file JSON:', parseError);
        return false;
      }

      if (!this.validateBackup(backup)) {
        log.error('Invalid backup file format');
        return false;
      }

      // 复制到备份目录
      const filename = path.basename(externalBackupPath);
      const targetPath = path.join(this.backupDir, filename);
      
      fs.copyFileSync(externalBackupPath, targetPath);
      log.info(`Backup imported: ${filename}`);
      
      return true;
    } catch (error) {
      log.error('Failed to import backup:', error);
      return false;
    }
  }
}

// 单例实例
export const configBackupManager = new ConfigBackupManager();

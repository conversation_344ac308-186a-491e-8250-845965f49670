import '@ant-design/v5-patch-for-react-19'; // React 19兼容性补丁
import { createRoot } from 'react-dom/client';
import 'antd/dist/reset.css';
import { App } from './App';
import { ThemeProvider } from './contexts/ThemeContext';
import { I18nProvider } from '../contexts/I18nContext';
import '../i18n'; // 初始化新的 i18n 系统
import type { ElectronAPI } from '@shared/ipc-types';

// 添加调试日志（仅开发环境）
if (process.env.NODE_ENV === 'development') {
  // 开发环境使用英文调试信息
  console.log('Renderer script loaded');
  console.log('electronAPI available:', !!window.electronAPI);
}

// 扩展Window接口以包含electronAPI
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

// 应用包装组件 - 现在由I18nProvider处理ConfigProvider
const AppWrapper: React.FC = () => {
  return <App />;
};

// 应用初始化
const container = document.getElementById('root');
if (container) {
  if (process.env.NODE_ENV === 'development') {
    // 开发环境使用英文调试信息
    console.log('Root container found, initializing React app...');
  }

  // 清除初始加载界面
  container.innerHTML = '';

  const root = createRoot(container);

  try {
    root.render(
      <I18nProvider>
        <ThemeProvider>
          <AppWrapper />
        </ThemeProvider>
      </I18nProvider>
    );
    if (process.env.NODE_ENV === 'development') {
      // 开发环境使用英文调试信息
      console.log('React app rendered successfully');
    }
  } catch (error) {
    // 错误信息使用英文，因为此时i18n可能还未初始化
    console.error('Failed to render React app:', error);
    container.innerHTML = `
      <div style="padding: 20px; color: red; font-family: monospace;">
        <h2>Application Startup Failed</h2>
        <p>Error: ${(error as Error).message}</p>
        <p>Please check the developer console for more information</p>
      </div>
    `;
  }
} else {
  console.error('Root container not found');
}

---
description: 
globs: 
alwaysApply: true
---
# Ant Design UI框架开发规范

## 使用场景
- 开发任何UI组件时
- 设计界面布局时
- 处理用户交互时
- 实现主题切换时
- 集成国际化功能时

## 关键规则
- **强制使用Ant Design组件**：禁止自定义实现已有的Ant Design组件
- **遵循Ant Design设计语言**：严格按照官方设计规范进行界面设计
- **使用ConfigProvider主题系统**：所有主题定制通过Ant Design官方主题系统
- **保持组件API一致性**：使用Ant Design标准API，避免不必要的封装
- **集成Apple风格设计**：在Ant Design基础上融入Apple设计语言元素

## Ant Design官方资源
- **官方文档**: https://ant-design.antgroup.com/index-cn
- **组件库**: https://ant-design.antgroup.com/components/overview-cn
- **设计规范**: https://ant-design.antgroup.com/docs/spec/introduce-cn
- **主题定制**: https://ant-design.antgroup.com/docs/react/customize-theme-cn

## 技术架构集成

### React + TypeScript + Ant Design
```typescript
import { ConfigProvider, theme } from 'antd';
import type { ThemeConfig } from 'antd';

// 主题配置
const themeConfig: ThemeConfig = {
  algorithm: theme.defaultAlgorithm,
  token: {
    colorPrimary: '#1890ff',
    borderRadius: 8,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto'
  }
};

// 根组件包装
function App() {
  return (
    <ConfigProvider theme={themeConfig}>
      {/* 应用内容 */}
    </ConfigProvider>
  );
}
```

### Electron + Ant Design 集成
```typescript
// 主进程窗口配置
const mainWindow = new BrowserWindow({
  webPreferences: {
    nodeIntegration: false,
    contextIsolation: true,
    enableRemoteModule: false
  },
  // 使用Ant Design建议的最小窗口尺寸
  minWidth: 800,
  minHeight: 600
});
```

## 组件使用规范

### 布局组件优先级
1. **Layout + Sider + Header + Content** - 主要布局容器
2. **Grid (Row + Col)** - 栅格布局，24栅格系统
3. **Space** - 组件间距，替代margin/padding
4. **Divider** - 分割线，替代border

### 导航组件标准
1. **Menu** - 主导航菜单，支持多级嵌套
2. **Breadcrumb** - 面包屑导航，集成React Router
3. **Pagination** - 分页，处理大量数据
4. **Steps** - 步骤条，展示流程进度

### 数据录入组件
1. **Form + Form.Item** - 表单容器，内置验证
2. **Input** - 输入框，支持各种类型
3. **Select** - 选择器，支持搜索和虚拟滚动
4. **Button** - 按钮，统一交互反馈
5. **Upload** - 文件上传，完整上传体验
6. **ColorPicker** - 颜色选择器（用于主题定制）

### 数据展示组件
1. **Table** - 表格，处理大量数据
2. **List** - 列表，支持无限滚动
3. **Card** - 卡片，内容容器
4. **Avatar** - 头像，支持图片懒加载
5. **Tag** - 标签，分类管理
6. **Badge** - 徽标，状态提示

### 反馈组件
1. **Alert** - 警告提示，支持操作
2. **Message** - 全局提示，不阻塞界面
3. **Notification** - 通知提醒，支持交互
4. **Modal** - 对话框，支持拖拽
5. **Drawer** - 抽屉，节省空间
6. **Progress** - 进度条，状态反馈

## Apple风格融合指南

### 视觉风格适配
```css
/* Ant Design + Apple风格定制 */
.ant-layout {
  /* 使用Apple系统字体 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto;
  
  /* Apple风格圆角 */
  border-radius: 12px;
  
  /* 毛玻璃效果 */
  backdrop-filter: blur(20px);
  background-color: rgba(255, 255, 255, 0.95);
}

/* Apple风格按钮 */
.ant-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
```

### 主题Token定制
```typescript
const appleStyleTheme: ThemeConfig = {
  token: {
    // Apple系统色彩
    colorPrimary: '#007AFF',
    colorSuccess: '#34C759',
    colorWarning: '#FF9500',
    colorError: '#FF3B30',
    
    // Apple字体系统
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
    fontSize: 14,
    fontSizeHeading1: 28,
    
    // Apple圆角系统
    borderRadius: 8,
    borderRadiusLG: 12,
    
    // Apple阴影系统
    boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)',
    
    // Apple动画
    motionDurationMid: '0.3s',
    motionEaseInOut: 'cubic-bezier(0.4, 0, 0.2, 1)'
  }
};
```

## 国际化集成

### Ant Design + i18next 集成
```typescript
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import enUS from 'antd/locale/en_US';
import ruRU from 'antd/locale/ru_RU';
import frFR from 'antd/locale/fr_FR';

const antdLocales = {
  'zh-CN': zhCN,
  'en': enUS,
  'ru': ruRU,
  'fr': frFR
};

function App() {
  const { i18n } = useTranslation();
  const currentLocale = antdLocales[i18n.language] || enUS;
  
  return (
    <ConfigProvider locale={currentLocale}>
      {/* 应用内容 */}
    </ConfigProvider>
  );
}
```

## 性能优化规范

### 组件按需加载
```typescript
// 推荐：按需导入
import { Button, Input, Form } from 'antd';

// 避免：全量导入
// import * from 'antd';
```

### 主题性能优化
```typescript
// 使用CSS-in-JS优化
const { token } = theme.useToken();

const styles = {
  container: {
    backgroundColor: token.colorBgContainer,
    borderRadius: token.borderRadius,
    padding: token.padding
  }
};
```

## 禁止行为

### ❌ 错误做法
```typescript
// 错误：重新实现Ant Design组件
const CustomButton = styled.button`
  /* 自定义按钮样式 */
`;

// 错误：绕过ConfigProvider直接修改样式
.ant-btn {
  color: red !important;
}

// 错误：不使用Ant Design的Form验证
const [errors, setErrors] = useState({});
```

### ✅ 正确做法
```typescript
// 正确：使用Ant Design组件
import { Button } from 'antd';

// 正确：通过ConfigProvider定制主题
<ConfigProvider theme={{ token: { colorPrimary: '#red' } }}>

// 正确：使用Ant Design Form验证
<Form.Item 
  name="email" 
  rules={[{ required: true, type: 'email' }]}
>
```

## 示例项目结构

```
src/
├── components/
│   ├── layout/
│   │   ├── AppLayout.tsx     # Ant Design Layout组件
│   │   ├── Sidebar.tsx       # Ant Design Sider组件
│   │   └── Header.tsx        # Ant Design Header组件
│   ├── forms/
│   │   ├── SettingsForm.tsx  # Ant Design Form组件
│   │   └── ThemeForm.tsx     # 主题设置表单
│   └── common/
│       ├── ThemeProvider.tsx # ConfigProvider包装
│       └── LocaleProvider.tsx # 国际化Provider
├── themes/
│   ├── default.ts           # 默认主题配置
│   ├── apple.ts             # Apple风格主题
│   └── dark.ts              # 暗黑模式主题
└── hooks/
    ├── useAntdTheme.ts      # Ant Design主题Hook
    └── useAntdLocale.ts     # Ant Design国际化Hook
```

## 测试规范

### 组件测试
```typescript
import { render, screen } from '@testing-library/react';
import { ConfigProvider } from 'antd';
import MyComponent from './MyComponent';

test('应该正确渲染Ant Design组件', () => {
  render(
    <ConfigProvider>
      <MyComponent />
    </ConfigProvider>
  );
  
  expect(screen.getByRole('button')).toBeInTheDocument();
});
```

### 主题测试
```typescript
test('主题切换应该正确应用', () => {
  const { rerender } = render(
    <ConfigProvider theme={lightTheme}>
      <MyComponent />
    </ConfigProvider>
  );
  
  rerender(
    <ConfigProvider theme={darkTheme}>
      <MyComponent />
    </ConfigProvider>
  );
  
  // 验证主题应用效果
});
```

## 版本兼容性

- **Ant Design版本**: 使用最新稳定版本 5.x
- **React版本**: 18.x 或更高
- **TypeScript版本**: 4.x 或更高
- **Electron版本**: 与Ant Design兼容的版本

## 最佳实践总结

1. **组件优先**: 优先使用Ant Design组件，减少自定义开发
2. **主题统一**: 通过ConfigProvider统一管理主题
3. **性能优化**: 按需导入，避免全量引入
4. **类型安全**: 使用TypeScript确保类型安全
5. **测试覆盖**: 为Ant Design组件编写测试
6. **文档同步**: 保持与官方文档同步更新
7. **Apple融合**: 在保持Ant Design设计语言的基础上融入Apple风格
8. **国际化集成**: 正确集成Ant Design的国际化功能


---
description: 
globs: 
alwaysApply: true
---
# 代码修改范围控制规范

## 使用场景
- 任何代码修改操作
- 文件创建和删除
- 功能实现和bug修复

## 关键规则
- 采用全局视角分析修改对系统的影响
- 最小化修改范围，只修改必要的代码部分
- 修改前先备份，修改后更新文档
- 确保相关模块协调工作，避免引入新问题
- 所有路径必须使用相对路径
- 新文件创建必须获得用户同意

## 修改流程
1. **问题定位** → 2. **影响评估** → 3. **方案设计** → 4. **备份执行** → 5. **代码修改** → 6. **测试验证** → 7. **文档更新**

## 影响评估检查项
- 前后端交互接口
- 数据流和状态管理
- API接口兼容性
- 依赖关系变化

## 示例

<example>
修改登录功能时的考虑：
1. 检查前端登录表单
2. 检查后端认证接口
3. 检查数据库用户表结构
4. 检查权限验证中间件
5. 更新相关模块文档
</example>

<example type="invalid">
直接修改单个文件而不考虑：
- 其他模块的依赖
- 接口兼容性
- 文档同步更新

</example>
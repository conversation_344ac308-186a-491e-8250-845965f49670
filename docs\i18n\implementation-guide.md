# QuickStart项目国际化(i18n)实现指南

## 📋 目录
- [系统架构](#系统架构)
- [文件结构](#文件结构)
- [基础使用方法](#基础使用方法)
- [高级功能](#高级功能)
- [添加新翻译](#添加新翻译)
- [添加新语言](#添加新语言)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

## 🏗️ 系统架构

### 核心组件
```
QuickStart i18n System
├── 核心配置 (src/i18n/index.ts)
├── React上下文 (src/contexts/I18nContext.tsx)
├── 翻译Hooks (src/hooks/)
├── 主进程支持 (src/main/i18n.ts)
└── 语言资源 (src/locales/)
```

### 技术栈
- **i18next**: 核心国际化引擎
- **react-i18next**: React集成
- **i18next-browser-languagedetector**: 语言检测
- **Ant Design**: UI组件国际化
- **TypeScript**: 类型安全保障

## 📁 文件结构

### 语言资源目录
```
src/locales/
├── zh-CN/              # 简体中文（默认语言）
│   ├── index.ts         # 语言资源导入
│   ├── common.json      # 通用翻译
│   ├── menu.json        # 菜单翻译
│   ├── theme.json       # 主题翻译
│   ├── settings.json    # 设置翻译
│   ├── errors.json      # 错误信息
│   ├── file.json        # 文件相关
│   ├── about.json       # 关于页面
│   └── config.json      # 配置相关
├── en/                  # 英语（回退语言）
├── ru/                  # 俄语
└── fr/                  # 法语
```

### 核心配置文件
```
src/i18n/
└── index.ts             # 主配置文件

src/hooks/
├── useTranslation.ts    # 标准翻译Hook
└── useTranslationWithMarker.ts  # 高级翻译Hook

src/contexts/
└── I18nContext.tsx      # React上下文

src/main/
└── i18n.ts              # 主进程i18n支持
```

## 🚀 基础使用方法

### 1. 在组件中使用翻译

#### 标准用法
```typescript
import { useTranslation } from '../../hooks/useTranslation';

const MyComponent: React.FC = () => {
  const { t } = useTranslation('common'); // 指定命名空间
  
  return (
    <div>
      <h1>{t('title', '默认标题')}</h1>
      <p>{t('description', '默认描述')}</p>
    </div>
  );
};
```

#### 多命名空间用法
```typescript
const { t } = useTranslation(['common', 'settings']);

// 使用不同命名空间
<h1>{t('common:title', '标题')}</h1>
<p>{t('settings:language.interface', '界面语言')}</p>
```

### 2. 语言切换

#### 使用I18nContext
```typescript
import { useI18nContext } from '../../contexts/I18nContext';

const LanguageSwitcher: React.FC = () => {
  const { changeLanguage, currentLanguage } = useI18nContext();
  
  const handleLanguageChange = async (lang: string) => {
    const success = await changeLanguage(lang as SupportedLanguage);
    if (success) {
      console.log(`语言已切换到: ${lang}`);
    }
  };
  
  return (
    <Select value={currentLanguage} onChange={handleLanguageChange}>
      <Option value="zh-CN">简体中文</Option>
      <Option value="en">English</Option>
      <Option value="ru">Русский</Option>
      <Option value="fr">Français</Option>
    </Select>
  );
};
```

### 3. 应用入口配置

#### 包装应用
```typescript
// src/renderer/index.tsx
import { I18nProvider } from '../contexts/I18nContext';
import '../i18n'; // 初始化i18n

const root = createRoot(document.getElementById('root')!);
root.render(
  <I18nProvider>
    <ThemeProvider>
      <App />
    </ThemeProvider>
  </I18nProvider>
);
```

## 🔧 高级功能

### 1. 使用useTranslationWithMarker

#### 文本标记和追踪
```typescript
import { useTranslationWithMarker } from '../../hooks/useTranslationWithMarker';

const AdvancedComponent: React.FC = () => {
  const { t } = useTranslationWithMarker('common', {
    componentName: 'AdvancedComponent',
    enableMarker: true,
    debug: process.env.NODE_ENV === 'development'
  });
  
  return (
    <div>
      {t('title', '标题', undefined, {
        elementType: 'heading',
        isDynamic: false,
        context: 'main-title'
      })}
    </div>
  );
};
```

### 2. 主进程国际化

#### 在主进程中使用
```typescript
// src/main/main.ts
import { mainI18n } from './i18n';

// 初始化主进程i18n
await mainI18n.initialize('zh-CN');

// 使用翻译
const menuLabel = mainI18n.t('menu.file', '文件');
const windowTitle = mainI18n.t('window.title', 'QuickStart');
```

### 3. 动态语言检测

#### 自动检测用户语言
```typescript
// 系统会自动检测以下来源的语言：
// 1. localStorage中保存的用户选择
// 2. 浏览器语言设置
// 3. HTML标签的lang属性
// 4. URL路径中的语言代码
// 5. 子域名中的语言代码
```

## ➕ 添加新翻译

### 1. 添加新的翻译键

#### 步骤1: 在JSON文件中添加翻译
```json
// src/locales/zh-CN/common.json
{
  "buttons": {
    "save": "保存",
    "cancel": "取消",
    "delete": "删除",
    "newButton": "新按钮"  // 新增翻译
  }
}
```

#### 步骤2: 为所有语言添加对应翻译
```json
// src/locales/en/common.json
{
  "buttons": {
    "save": "Save",
    "cancel": "Cancel", 
    "delete": "Delete",
    "newButton": "New Button"  // 对应的英文翻译
  }
}
```

#### 步骤3: 在组件中使用
```typescript
const { t } = useTranslation('common');

<Button>{t('buttons.newButton', '新按钮')}</Button>
```

### 2. 添加新的命名空间

#### 步骤1: 创建新的JSON文件
```json
// src/locales/zh-CN/dashboard.json
{
  "title": "仪表板",
  "widgets": {
    "chart": "图表",
    "table": "表格",
    "summary": "摘要"
  },
  "actions": {
    "refresh": "刷新",
    "export": "导出"
  }
}
```

#### 步骤2: 更新语言资源导入
```typescript
// src/locales/zh-CN/index.ts
import dashboard from './dashboard.json';  // 新增导入

export default {
  common,
  menu,
  theme,
  settings,
  errors,
  file,
  about,
  config,
  dashboard,  // 新增命名空间
};
```

#### 步骤3: 更新主配置
```typescript
// src/i18n/index.ts
export type TranslationNamespace = 
  | 'common' 
  | 'menu' 
  | 'theme' 
  | 'settings' 
  | 'errors' 
  | 'file' 
  | 'about' 
  | 'config'
  | 'dashboard';  // 新增类型

// 更新命名空间配置
ns: ['common', 'menu', 'theme', 'settings', 'errors', 'file', 'about', 'config', 'dashboard'],
```

#### 步骤4: 在组件中使用
```typescript
const { t } = useTranslation('dashboard');

<h1>{t('title', '仪表板')}</h1>
<Button>{t('actions.refresh', '刷新')}</Button>
```

## 🌍 添加新语言

### 1. 创建语言目录和文件

#### 步骤1: 创建语言目录
```bash
mkdir src/locales/ja  # 添加日语支持
```

#### 步骤2: 复制并翻译所有JSON文件
```bash
# 复制中文文件作为模板
cp -r src/locales/zh-CN/*.json src/locales/ja/
```

#### 步骤3: 翻译内容
```json
// src/locales/ja/common.json
{
  "buttons": {
    "save": "保存",      // 翻译为日语
    "cancel": "キャンセル",
    "delete": "削除"
  },
  "language": {
    "name": "日本語",
    "selector": {
      "title": "言語を選択",
      "zh-CN": "简体中文",
      "en": "English",
      "ru": "Русский", 
      "fr": "Français",
      "ja": "日本語"    // 添加日语选项
    }
  }
}
```

### 2. 更新系统配置

#### 步骤1: 创建语言资源导入文件
```typescript
// src/locales/ja/index.ts
import common from './common.json';
import menu from './menu.json';
// ... 导入所有命名空间

export default {
  common,
  menu,
  theme,
  settings,
  errors,
  file,
  about,
  config,
};
```

#### 步骤2: 更新主配置
```typescript
// src/i18n/index.ts
import ja from '../locales/ja';  // 导入日语资源

// 更新支持的语言列表
export const supportedLanguages = [
  { code: 'zh-CN', name: '简体中文', nativeName: '简体中文' },
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'ru', name: 'Русский', nativeName: 'Русский' },
  { code: 'fr', name: 'Français', nativeName: 'Français' },
  { code: 'ja', name: '日本語', nativeName: '日本語' },  // 新增
];

// 更新语言资源
const resources = {
  'zh-CN': zhCN,
  'en': en,
  'ru': ru,
  'fr': fr,
  'ja': ja,  // 新增
};

// 更新类型定义
export type SupportedLanguage = 'zh-CN' | 'en' | 'ru' | 'fr' | 'ja';
```

#### 步骤3: 添加Ant Design语言包支持
```typescript
// src/contexts/I18nContext.tsx
const antdLocales: Record<SupportedLanguage, () => Promise<{ default: Locale }>> = {
  'zh-CN': () => import('antd/locale/zh_CN'),
  'en': () => import('antd/locale/en_US'),
  'ru': () => import('antd/locale/ru_RU'),
  'fr': () => import('antd/locale/fr_FR'),
  'ja': () => import('antd/locale/ja_JP'),  // 新增日语支持
};
```

### 3. 更新语言检测映射
```typescript
// src/i18n/index.ts
convertDetectedLanguage: (lng: string) => {
  const languageMap: Record<string, string> = {
    'en-US': 'en',
    'ru-RU': 'ru',
    'fr-FR': 'fr',
    'zh-TW': 'zh-CN',
    'ja-JP': 'ja',  // 新增日语映射
  };
  return languageMap[lng] || lng;
},
```

## 📝 最佳实践

### 1. 翻译键命名规范
```typescript
// ✅ 推荐：使用层级结构
"user.profile.name": "用户名"
"user.profile.email": "邮箱"
"user.actions.save": "保存"

// ❌ 不推荐：扁平结构
"userName": "用户名"
"userEmail": "邮箱"
"saveUser": "保存"
```

### 2. 始终提供默认值
```typescript
// ✅ 推荐：提供默认值
t('user.name', '用户名')

// ❌ 不推荐：没有默认值
t('user.name')
```

### 3. 使用命名空间组织翻译
```typescript
// ✅ 推荐：按功能模块组织
const { t } = useTranslation('settings');
t('language.interface', '界面语言')

// ❌ 不推荐：所有翻译放在一个文件
t('settingsLanguageInterface', '界面语言')
```

### 4. 处理复数和变量
```typescript
// 复数处理
t('items', '{{count}} 个项目', { count: 5 })

// 变量插值
t('welcome', '欢迎 {{name}}！', { name: '张三' })

// 格式化
t('date', '{{date, datetime}}', { 
  date: new Date(),
  formatParams: {
    date: { year: 'numeric', month: 'long', day: 'numeric' }
  }
})
```

## 🔧 故障排除

### 1. 常见问题

#### 翻译不显示
```typescript
// 检查命名空间是否正确
const { t } = useTranslation('correct-namespace');

// 检查翻译键是否存在
console.log(t('existing.key', '默认值'));

// 检查语言文件是否正确加载
console.log(i18n.getResourceBundle('zh-CN', 'common'));
```

#### 语言切换不生效
```typescript
// 检查I18nProvider是否正确包装应用
<I18nProvider>
  <App />
</I18nProvider>

// 检查语言代码是否正确
const { changeLanguage } = useI18nContext();
await changeLanguage('zh-CN'); // 使用正确的语言代码
```

#### TypeScript类型错误
```typescript
// 确保导入正确的类型
import type { SupportedLanguage } from '../i18n';

// 使用类型断言
const language = 'zh-CN' as SupportedLanguage;
```

### 2. 调试技巧

#### 启用调试模式
```typescript
// src/i18n/index.ts
debug: process.env.NODE_ENV === 'development',
```

#### 检查翻译覆盖率
```typescript
// 使用useTranslationWithMarker追踪文本使用
const { getRegisteredElements } = useTranslationWithMarker('common', {
  componentName: 'MyComponent',
  enableMarker: true,
  debug: true
});

console.log('注册的文本元素:', getRegisteredElements());
```

#### 监听语言变化
```typescript
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

const { i18n } = useTranslation();

useEffect(() => {
  const handleLanguageChanged = (lng: string) => {
    console.log('语言已切换到:', lng);
  };
  
  i18n.on('languageChanged', handleLanguageChanged);
  
  return () => {
    i18n.off('languageChanged', handleLanguageChanged);
  };
}, [i18n]);
```

## 📚 参考资源

### 官方文档
- [i18next官方文档](https://www.i18next.com/)
- [react-i18next官方文档](https://react.i18next.com/)
- [Ant Design国际化](https://ant.design/docs/react/i18n)

### 项目文档
- [重构报告](./i18n-refactoring-report.md)
- [完成报告](./i18n-refactoring-completion.md)
- [完成度报告](./completion-report.md)

## 🎯 实际应用示例

### 1. 完整的设置页面示例
```typescript
// SettingsLanguagePanel.tsx
import React from 'react';
import { Card, Select, Row, Col, Typography, Divider } from 'antd';
import { useTranslation } from '../../hooks/useTranslation';
import { useI18nContext } from '../../contexts/I18nContext';
import { supportedLanguages } from '../../i18n';

const { Text } = Typography;
const { Option } = Select;

export const SettingsLanguagePanel: React.FC = () => {
  const { t } = useTranslation('settings');
  const { changeLanguage, currentLanguage, isLoading } = useI18nContext();

  const handleLanguageChange = async (language: string) => {
    const success = await changeLanguage(language as SupportedLanguage);
    if (success) {
      // 语言切换成功，界面会自动更新
      console.log(`语言已切换到: ${language}`);
    }
  };

  return (
    <Card title={t('language.title', '语言设置')}>
      <Row gutter={16} align="middle">
        <Col span={6}>
          <Text>{t('language.interface', '界面语言')}:</Text>
        </Col>
        <Col span={18}>
          <Select
            value={currentLanguage}
            onChange={handleLanguageChange}
            loading={isLoading}
            style={{ width: '100%' }}
          >
            {supportedLanguages.map(lang => (
              <Option key={lang.code} value={lang.code}>
                {lang.nativeName}
              </Option>
            ))}
          </Select>
        </Col>
      </Row>

      <Divider />

      <Row gutter={16} align="middle">
        <Col span={6}>
          <Text>{t('language.current', '当前语言')}:</Text>
        </Col>
        <Col span={18}>
          <Text type="secondary">{currentLanguage}</Text>
        </Col>
      </Row>
    </Card>
  );
};
```

### 2. 动态表单示例
```typescript
// DynamicForm.tsx
import React from 'react';
import { Form, Input, Button, message } from 'antd';
import { useTranslation } from '../../hooks/useTranslation';

interface FormData {
  name: string;
  email: string;
  description: string;
}

export const DynamicForm: React.FC = () => {
  const { t } = useTranslation(['common', 'form']);
  const [form] = Form.useForm();

  const onFinish = (values: FormData) => {
    message.success(t('common:messages.operationSuccess', '操作成功'));
    console.log('表单数据:', values);
  };

  const onFinishFailed = () => {
    message.error(t('form:validation.failed', '表单验证失败'));
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      onFinishFailed={onFinishFailed}
    >
      <Form.Item
        label={t('form:fields.name', '姓名')}
        name="name"
        rules={[
          {
            required: true,
            message: t('form:validation.required', '此字段为必填项')
          }
        ]}
      >
        <Input placeholder={t('form:placeholders.enterName', '请输入姓名')} />
      </Form.Item>

      <Form.Item
        label={t('form:fields.email', '邮箱')}
        name="email"
        rules={[
          {
            required: true,
            message: t('form:validation.required', '此字段为必填项')
          },
          {
            type: 'email',
            message: t('form:validation.invalidEmail', '邮箱格式无效')
          }
        ]}
      >
        <Input placeholder={t('form:placeholders.enterEmail', '请输入邮箱')} />
      </Form.Item>

      <Form.Item
        label={t('form:fields.description', '描述')}
        name="description"
      >
        <Input.TextArea
          rows={4}
          placeholder={t('form:placeholders.enterDescription', '请输入描述')}
        />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit">
          {t('common:buttons.submit', '提交')}
        </Button>
        <Button style={{ marginLeft: 8 }} onClick={() => form.resetFields()}>
          {t('common:buttons.reset', '重置')}
        </Button>
      </Form.Item>
    </Form>
  );
};
```

### 3. 错误处理示例
```typescript
// ErrorBoundary.tsx
import React from 'react';
import { Result, Button } from 'antd';
import { useTranslation } from '../../hooks/useTranslation';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback onRetry={() => this.setState({ hasError: false })} />;
    }

    return this.props.children;
  }
}

const ErrorFallback: React.FC<{ onRetry: () => void }> = ({ onRetry }) => {
  const { t } = useTranslation('errors');

  return (
    <Result
      status="error"
      title={t('boundary.title', '出现了错误')}
      subTitle={t('boundary.subtitle', '抱歉，页面出现了意外错误')}
      extra={[
        <Button type="primary" onClick={onRetry} key="retry">
          {t('boundary.retry', '重试')}
        </Button>,
        <Button key="home" onClick={() => window.location.href = '/'}>
          {t('boundary.backHome', '返回首页')}
        </Button>
      ]}
    />
  );
};
```

## 🔄 批量操作工具

### 1. 批量添加翻译脚本
```javascript
// scripts/add-translations.js
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 批量添加翻译键到所有语言文件
 */
function addTranslationKey(namespace, keyPath, translations) {
  const languages = ['zh-CN', 'en', 'ru', 'fr'];

  languages.forEach(lang => {
    const filePath = path.join('src/locales', lang, `${namespace}.json`);

    if (fs.existsSync(filePath)) {
      const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));

      // 使用点号路径设置嵌套值
      setNestedValue(content, keyPath, translations[lang]);

      fs.writeFileSync(filePath, JSON.stringify(content, null, 2), 'utf8');
      console.log(`✅ 已更新 ${lang}/${namespace}.json`);
    }
  });
}

function setNestedValue(obj, path, value) {
  const keys = path.split('.');
  let current = obj;

  for (let i = 0; i < keys.length - 1; i++) {
    if (!(keys[i] in current)) {
      current[keys[i]] = {};
    }
    current = current[keys[i]];
  }

  current[keys[keys.length - 1]] = value;
}

// 使用示例
addTranslationKey('common', 'buttons.export', {
  'zh-CN': '导出',
  'en': 'Export',
  'ru': 'Экспорт',
  'fr': 'Exporter'
});

addTranslationKey('form', 'validation.minLength', {
  'zh-CN': '最少需要 {{min}} 个字符',
  'en': 'Minimum {{min}} characters required',
  'ru': 'Требуется минимум {{min}} символов',
  'fr': 'Minimum {{min}} caractères requis'
});
```

### 2. 翻译覆盖率检查脚本
```javascript
// scripts/check-translation-coverage.js
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 检查翻译覆盖率
 */
function checkTranslationCoverage() {
  const languages = ['zh-CN', 'en', 'ru', 'fr'];
  const namespaces = ['common', 'menu', 'theme', 'settings', 'errors', 'file', 'about', 'config', 'logs'];

  const coverage = {};

  // 以中文为基准检查其他语言的覆盖率
  namespaces.forEach(namespace => {
    const zhFile = path.join('src/locales/zh-CN', `${namespace}.json`);
    if (!fs.existsSync(zhFile)) return;

    const zhContent = JSON.parse(fs.readFileSync(zhFile, 'utf8'));
    const zhKeys = getAllKeys(zhContent);

    coverage[namespace] = {};

    languages.forEach(lang => {
      const langFile = path.join('src/locales', lang, `${namespace}.json`);
      if (!fs.existsSync(langFile)) {
        coverage[namespace][lang] = { total: zhKeys.length, missing: zhKeys.length, rate: 0 };
        return;
      }

      const langContent = JSON.parse(fs.readFileSync(langFile, 'utf8'));
      const langKeys = getAllKeys(langContent);

      const missing = zhKeys.filter(key => !langKeys.includes(key));
      const rate = ((zhKeys.length - missing.length) / zhKeys.length * 100).toFixed(1);

      coverage[namespace][lang] = {
        total: zhKeys.length,
        missing: missing.length,
        rate: parseFloat(rate),
        missingKeys: missing
      };
    });
  });

  // 输出报告
  console.log('\n📊 翻译覆盖率报告\n');

  namespaces.forEach(namespace => {
    if (!coverage[namespace]) return;

    console.log(`📁 ${namespace}:`);
    languages.forEach(lang => {
      const data = coverage[namespace][lang];
      const status = data.rate === 100 ? '✅' : data.rate >= 80 ? '⚠️' : '❌';
      console.log(`  ${status} ${lang}: ${data.rate}% (${data.total - data.missing}/${data.total})`);

      if (data.missingKeys && data.missingKeys.length > 0) {
        console.log(`    缺失: ${data.missingKeys.slice(0, 3).join(', ')}${data.missingKeys.length > 3 ? '...' : ''}`);
      }
    });
    console.log('');
  });
}

function getAllKeys(obj, prefix = '') {
  let keys = [];

  for (const key in obj) {
    const fullKey = prefix ? `${prefix}.${key}` : key;

    if (typeof obj[key] === 'object' && obj[key] !== null) {
      keys = keys.concat(getAllKeys(obj[key], fullKey));
    } else {
      keys.push(fullKey);
    }
  }

  return keys;
}

// 运行检查
checkTranslationCoverage();
```

### 3. 自动翻译脚本（使用翻译API）
```javascript
// scripts/auto-translate.js
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 自动翻译缺失的键（需要配置翻译API）
 * 注意：这只是示例，实际使用时需要配置真实的翻译服务
 */
async function autoTranslate(sourceText, targetLang) {
  // 这里可以集成Google Translate API、百度翻译API等
  // 示例使用模拟翻译
  const mockTranslations = {
    'en': {
      '导出': 'Export',
      '导入': 'Import',
      '设置': 'Settings'
    },
    'ru': {
      '导出': 'Экспорт',
      '导入': 'Импорт',
      '设置': 'Настройки'
    },
    'fr': {
      '导出': 'Exporter',
      '导入': 'Importer',
      '设置': 'Paramètres'
    }
  };

  return mockTranslations[targetLang]?.[sourceText] || `[AUTO] ${sourceText}`;
}

async function fillMissingTranslations() {
  const languages = ['en', 'ru', 'fr']; // 不包括源语言zh-CN
  const namespaces = ['common', 'menu', 'theme', 'settings'];

  for (const namespace of namespaces) {
    const zhFile = path.join('src/locales/zh-CN', `${namespace}.json`);
    if (!fs.existsSync(zhFile)) continue;

    const zhContent = JSON.parse(fs.readFileSync(zhFile, 'utf8'));

    for (const lang of languages) {
      const langFile = path.join('src/locales', lang, `${namespace}.json`);
      let langContent = {};

      if (fs.existsSync(langFile)) {
        langContent = JSON.parse(fs.readFileSync(langFile, 'utf8'));
      }

      const updated = await fillMissingKeys(zhContent, langContent, lang);

      if (updated) {
        fs.writeFileSync(langFile, JSON.stringify(langContent, null, 2), 'utf8');
        console.log(`✅ 已更新 ${lang}/${namespace}.json`);
      }
    }
  }
}

async function fillMissingKeys(source, target, targetLang, prefix = '') {
  let updated = false;

  for (const key in source) {
    const fullKey = prefix ? `${prefix}.${key}` : key;

    if (typeof source[key] === 'object' && source[key] !== null) {
      if (!target[key]) {
        target[key] = {};
        updated = true;
      }
      const childUpdated = await fillMissingKeys(source[key], target[key], targetLang, fullKey);
      updated = updated || childUpdated;
    } else {
      if (!target[key]) {
        target[key] = await autoTranslate(source[key], targetLang);
        updated = true;
        console.log(`  + ${fullKey}: "${source[key]}" -> "${target[key]}"`);
      }
    }
  }

  return updated;
}

// 运行自动翻译
fillMissingTranslations().catch(console.error);
```

---

**文档版本**: v1.0
**最后更新**: 2025-01-04
**维护者**: QuickStart开发团队

---
description: 当开发者输入"/测试"指令时使用
globs: 
alwaysApply: false
---
# 测试指令处理规范

## 使用场景
- 用户输入`/测试`指令时
- 需要创建或执行测试用例时
- 验证功能正确性时

## 关键规则
- **Context7优先**：测试前必须使用Context7调研相关测试框架和最佳实践
- 根据项目类型选择合适的测试框架
- 创建全面的测试用例覆盖核心功能
- 执行测试并生成详细报告
- 在模块文档中记录测试覆盖情况
- 识别测试失败原因并提供修复建议

## 测试类型
- **单元测试**: 测试单个函数或类的功能
- **集成测试**: 测试模块间的交互
- **功能测试**: 测试完整的业务流程
- **API测试**: 测试接口的输入输出

## 测试流程
1. **需求分析** → 2. **Context7调研** → 3. **测试设计** → 4. **用例编写** → 5. **测试执行** → 6. **结果分析** → 7. **文档更新**

## 示例

<example>
```text
用户：/测试 用户认证模块

AI回复：
🧪 开始测试用户认证模块...

🔍 Context7调研：
- 查询Electron项目测试框架最佳实践
- 调研React组件测试方法
- 查询Node.js API测试工具

📋 测试计划：
- 单元测试：密码验证函数
- 集成测试：登录API接口
- 功能测试：完整登录流程

✅ 创建测试用例：
- test_password_validation() ✅
- test_login_api() ✅  
- test_login_flow() ❌ 失败

📊 测试结果：
- 总用例：15个
- 通过：13个
- 失败：2个
- 覆盖率：87%

🔍 失败分析：
1. test_login_flow(): 密码为空时未正确返回错误
2. test_token_refresh(): Token过期处理逻辑错误

🔧 修复建议：
1. 在登录接口添加密码非空验证
2. 完善Token刷新机制

📝 更新文档：
- ✅ 已更新docs/auth.md测试覆盖情况
- ✅ 已记录待修复的测试问题

测试完成！发现2个需要修复的问题。
```
</example>

<example type="invalid">
```text
"测试通过了。"
（错误：没有Context7调研，没有具体测试内容，没有测试报告，没有文档更新）
```

</example>
# QuickStart项目国际化(i18n)实现指南

## 📖 概述

QuickStart的国际化系统基于i18next框架构建，提供完整的多语言支持，实现了实时语言切换、自动检测和配置持久化功能。

## ✅ 实现状态

**完成度**: 100% ✅
**最后验证**: 2025-07-07
**状态**: 生产就绪

### 🔧 已修复问题 (2025-07-07)
- **ERROR-116**: i18n Context重复定义 - 删除重复实现，统一使用主Context
- **ERROR-114**: 国际化配置硬编码 - 验证硬编码配置的合理性
- **ERROR-117**: 语言切换无页面刷新机制 - 实现自动页面刷新功能

## 📋 目录
- [系统架构](#系统架构)
- [文件结构](#文件结构)
- [基础使用方法](#基础使用方法)
- [高级功能](#高级功能)
- [添加新翻译](#添加新翻译)
- [添加新语言](#添加新语言)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

## 🌍 支持的语言

| 语言代码 | 语言名称 | 本地名称 | 状态 |
|---------|---------|---------|------|
| zh-CN | 简体中文 | 简体中文 | ✅ 完整 |
| en | 英语 | English | ✅ 完整 |
| ru | 俄语 | Русский | ✅ 完整 |
| fr | 法语 | Français | ✅ 完整 |

## 🏗️ 系统架构

### 核心组件
```
QuickStart i18n System
├── 核心配置 (src/i18n/index.ts)
├── React上下文 (src/contexts/I18nContext.tsx)
├── 翻译Hooks (src/hooks/)
├── 主进程支持 (src/main/i18n.ts)
└── 语言资源 (src/locales/)
```

### 技术栈
- **i18next**: 核心国际化引擎
- **react-i18next**: React集成
- **i18next-browser-languagedetector**: 语言检测
- **Ant Design**: UI组件国际化
- **TypeScript**: 类型安全保障

## 📁 文件结构

### 语言资源目录
```
src/locales/
├── zh-CN/              # 简体中文（默认语言）
│   ├── index.ts         # 语言资源导入
│   ├── common.json      # 通用翻译
│   ├── menu.json        # 菜单翻译
│   ├── theme.json       # 主题翻译
│   ├── settings.json    # 设置翻译
│   ├── errors.json      # 错误信息
│   ├── file.json        # 文件相关
│   ├── about.json       # 关于页面
│   └── config.json      # 配置相关
├── en/                  # 英语（回退语言）
├── ru/                  # 俄语
└── fr/                  # 法语
```

### 核心配置文件
```
src/i18n/
└── index.ts             # 主配置文件

src/hooks/
├── useTranslation.ts    # 标准翻译Hook
└── useTranslationWithMarker.ts  # 高级翻译Hook

src/contexts/
└── I18nContext.tsx      # React上下文

src/main/
└── i18n.ts              # 主进程i18n支持
```

## � 基础使用方法

### 1. 在组件中使用翻译

#### 标准用法
```typescript
import { useTranslation } from '../../hooks/useTranslation';

const MyComponent: React.FC = () => {
  const { t } = useTranslation('common'); // 指定命名空间

  return (
    <div>
      <h1>{t('title', '默认标题')}</h1>
      <p>{t('description', '默认描述')}</p>
    </div>
  );
};
```

#### 多命名空间用法
```typescript
const { t } = useTranslation(['common', 'settings']);

// 使用不同命名空间
<h1>{t('common:title', '标题')}</h1>
<p>{t('settings:language.interface', '界面语言')}</p>
```

### 2. 语言切换

#### 使用I18nContext
```typescript
import { useI18nContext } from '../../contexts/I18nContext';

const LanguageSwitcher: React.FC = () => {
  const { changeLanguage, currentLanguage } = useI18nContext();

  const handleLanguageChange = async (lang: string) => {
    const success = await changeLanguage(lang as SupportedLanguage);
    if (success) {
      console.log(`语言已切换到: ${lang}`);
    }
  };

  return (
    <Select value={currentLanguage} onChange={handleLanguageChange}>
      <Option value="zh-CN">简体中文</Option>
      <Option value="en">English</Option>
      <Option value="ru">Русский</Option>
      <Option value="fr">Français</Option>
    </Select>
  );
};
```

### 3. 应用入口配置

#### 包装应用
```typescript
// src/renderer/index.tsx
import { I18nProvider } from '../contexts/I18nContext';
import '../i18n'; // 初始化i18n

const root = createRoot(document.getElementById('root')!);
root.render(
  <I18nProvider>
    <ThemeProvider>
      <App />
    </ThemeProvider>
  </I18nProvider>
);
```

## 🔧 高级功能

### 1. 使用useTranslationWithMarker

#### 文本标记和追踪
```typescript
import { useTranslationWithMarker } from '../../hooks/useTranslationWithMarker';

const AdvancedComponent: React.FC = () => {
  const { t } = useTranslationWithMarker('common', {
    componentName: 'AdvancedComponent',
    enableMarker: true,
    debug: process.env.NODE_ENV === 'development'
  });

  return (
    <div>
      {t('title', '标题', undefined, {
        elementType: 'heading',
        isDynamic: false,
        context: 'main-title'
      })}
    </div>
  );
};
```

### 2. 主进程国际化

#### 在主进程中使用
```typescript
// src/main/main.ts
import { mainI18n } from './i18n';

// 初始化主进程i18n
await mainI18n.initialize('zh-CN');

// 使用翻译
const menuLabel = mainI18n.t('menu.file', '文件');
const windowTitle = mainI18n.t('window.title', 'QuickStart');
```

### 3. 动态语言检测

#### 自动检测用户语言
```typescript
// 系统会自动检测以下来源的语言：
// 1. localStorage中保存的用户选择
// 2. 浏览器语言设置
// 3. HTML标签的lang属性
// 4. URL路径中的语言代码
// 5. 子域名中的语言代码
```

## ➕ 添加新翻译

### 1. 添加新的翻译键

#### 步骤1: 在JSON文件中添加翻译
```json
// src/locales/zh-CN/common.json
{
  "buttons": {
    "save": "保存",
    "cancel": "取消",
    "delete": "删除",
    "newButton": "新按钮"  // 新增翻译
  }
}
```

#### 步骤2: 为所有语言添加对应翻译
```json
// src/locales/en/common.json
{
  "buttons": {
    "save": "Save",
    "cancel": "Cancel",
    "delete": "Delete",
    "newButton": "New Button"  // 对应的英文翻译
  }
}
```

#### 步骤3: 在组件中使用
```typescript
const { t } = useTranslation('common');

<Button>{t('buttons.newButton', '新按钮')}</Button>
```

### 2. 添加新的命名空间

#### 步骤1: 创建新的JSON文件
```json
// src/locales/zh-CN/dashboard.json
{
  "title": "仪表板",
  "widgets": {
    "chart": "图表",
    "table": "表格",
    "summary": "摘要"
  },
  "actions": {
    "refresh": "刷新",
    "export": "导出"
  }
}
```

#### 步骤2: 更新语言资源导入
```typescript
// src/locales/zh-CN/index.ts
import dashboard from './dashboard.json';  // 新增导入

export default {
  common,
  menu,
  theme,
  settings,
  errors,
  file,
  about,
  config,
  dashboard,  // 新增命名空间
};
```

#### 步骤3: 更新主配置
```typescript
// src/i18n/index.ts
export type TranslationNamespace =
  | 'common'
  | 'menu'
  | 'theme'
  | 'settings'
  | 'errors'
  | 'file'
  | 'about'
  | 'config'
  | 'dashboard';  // 新增类型

// 更新命名空间配置
ns: ['common', 'menu', 'theme', 'settings', 'errors', 'file', 'about', 'config', 'dashboard'],
```

#### 步骤4: 在组件中使用
```typescript
const { t } = useTranslation('dashboard');

<h1>{t('title', '仪表板')}</h1>
<Button>{t('actions.refresh', '刷新')}</Button>
```

## 🌍 添加新语言

### 1. 创建语言目录和文件

#### 步骤1: 创建语言目录
```bash
mkdir src/locales/ja  # 添加日语支持
```

#### 步骤2: 复制并翻译所有JSON文件
```bash
# 复制中文文件作为模板
cp -r src/locales/zh-CN/*.json src/locales/ja/
```

#### 步骤3: 翻译内容
```json
// src/locales/ja/common.json
{
  "buttons": {
    "save": "保存",      // 翻译为日语
    "cancel": "キャンセル",
    "delete": "削除"
  },
  "language": {
    "name": "日本語",
    "selector": {
      "title": "言語を選択",
      "zh-CN": "简体中文",
      "en": "English",
      "ru": "Русский",
      "fr": "Français",
      "ja": "日本語"    // 添加日语选项
    }
  }
}
```

### 2. 更新系统配置

#### 步骤1: 创建语言资源导入文件
```typescript
// src/locales/ja/index.ts
import common from './common.json';
import menu from './menu.json';
// ... 导入所有命名空间

export default {
  common,
  menu,
  theme,
  settings,
  errors,
  file,
  about,
  config,
};
```

#### 步骤2: 更新主配置
```typescript
// src/i18n/index.ts
import ja from '../locales/ja';  // 导入日语资源

// 更新支持的语言列表
export const supportedLanguages = [
  { code: 'zh-CN', name: '简体中文', nativeName: '简体中文' },
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'ru', name: 'Русский', nativeName: 'Русский' },
  { code: 'fr', name: 'Français', nativeName: 'Français' },
  { code: 'ja', name: '日本語', nativeName: '日本語' },  // 新增
];

// 更新语言资源
const resources = {
  'zh-CN': zhCN,
  'en': en,
  'ru': ru,
  'fr': fr,
  'ja': ja,  // 新增
};

// 更新类型定义
export type SupportedLanguage = 'zh-CN' | 'en' | 'ru' | 'fr' | 'ja';
```

#### 步骤3: 添加Ant Design语言包支持
```typescript
// src/contexts/I18nContext.tsx
const antdLocales: Record<SupportedLanguage, () => Promise<{ default: Locale }>> = {
  'zh-CN': () => import('antd/locale/zh_CN'),
  'en': () => import('antd/locale/en_US'),
  'ru': () => import('antd/locale/ru_RU'),
  'fr': () => import('antd/locale/fr_FR'),
  'ja': () => import('antd/locale/ja_JP'),  // 新增日语支持
};
```

### 3. 更新语言检测映射
```typescript
// src/i18n/index.ts
convertDetectedLanguage: (lng: string) => {
  const languageMap: Record<string, string> = {
    'en-US': 'en',
    'ru-RU': 'ru',
    'fr-FR': 'fr',
    'zh-TW': 'zh-CN',
    'ja-JP': 'ja',  // 新增日语映射
  };
  return languageMap[lng] || lng;
},
```

## � 最佳实践

### 1. 翻译键命名规范
```typescript
// ✅ 推荐：使用层级结构
"user.profile.name": "用户名"
"user.profile.email": "邮箱"
"user.actions.save": "保存"

// ❌ 不推荐：扁平结构
"userName": "用户名"
"userEmail": "邮箱"
"saveUser": "保存"
```

### 2. 始终提供默认值
```typescript
// ✅ 推荐：提供默认值
t('user.name', '用户名')

// ❌ 不推荐：没有默认值
t('user.name')
```

### 3. 使用命名空间组织翻译
```typescript
// ✅ 推荐：按功能模块组织
const { t } = useTranslation('settings');
t('language.interface', '界面语言')

// ❌ 不推荐：所有翻译放在一个文件
t('settingsLanguageInterface', '界面语言')
```

### 4. 处理复数和变量
```typescript
// 复数处理
t('items', '{{count}} 个项目', { count: 5 })

// 变量插值
t('welcome', '欢迎 {{name}}！', { name: '张三' })

// 格式化
t('date', '{{date, datetime}}', {
  date: new Date(),
  formatParams: {
    date: { year: 'numeric', month: 'long', day: 'numeric' }
  }
})
```

## 🔧 故障排除

### 1. 常见问题

#### 翻译不显示
```typescript
// 检查命名空间是否正确
const { t } = useTranslation('correct-namespace');

// 检查翻译键是否存在
console.log(t('existing.key', '默认值'));

// 检查语言文件是否正确加载
console.log(i18n.getResourceBundle('zh-CN', 'common'));
```

#### 语言切换不生效
```typescript
// 检查I18nProvider是否正确包装应用
<I18nProvider>
  <App />
</I18nProvider>

// 检查语言代码是否正确
const { changeLanguage } = useI18nContext();
await changeLanguage('zh-CN'); // 使用正确的语言代码
```

#### TypeScript类型错误
```typescript
// 确保导入正确的类型
import type { SupportedLanguage } from '../i18n';

// 使用类型断言
const language = 'zh-CN' as SupportedLanguage;
```

### 2. 调试技巧

#### 启用调试模式
```typescript
// src/i18n/index.ts
debug: process.env.NODE_ENV === 'development',
```

#### 检查翻译覆盖率
```typescript
// 使用useTranslationWithMarker追踪文本使用
const { getRegisteredElements } = useTranslationWithMarker('common', {
  componentName: 'MyComponent',
  enableMarker: true,
  debug: true
});

console.log('注册的文本元素:', getRegisteredElements());
```

#### 监听语言变化
```typescript
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

const { i18n } = useTranslation();

useEffect(() => {
  const handleLanguageChanged = (lng: string) => {
    console.log('语言已切换到:', lng);
  };

  i18n.on('languageChanged', handleLanguageChanged);

  return () => {
    i18n.off('languageChanged', handleLanguageChanged);
  };
}, [i18n]);
```

## �🐛 已解决的问题

### ERROR-004: FileList组件国际化违规 ✅
- **问题**: 30+个硬编码中文文本
- **解决**: 添加file命名空间，完整i18n支持

### ERROR-005: HTML模板语言硬编码 ✅
- **问题**: HTML lang属性硬编码为zh-CN
- **解决**: 动态设置lang属性，支持浏览器语言检测

### ERROR-008: StylesPanel组件国际化违规 ✅
- **问题**: 45个硬编码中文文本
- **解决**: 扩展theme命名空间，完整i18n支持

### ERROR-009: SettingsPanel组件国际化违规 ✅
- **问题**: 86个硬编码中文文本
- **解决**: 扩展settings命名空间，完整i18n支持

### ERROR-010: AboutPanel组件国际化违规 ✅
- **问题**: 43个硬编码中文文本
- **解决**: 创建about命名空间，完整i18n支持

### ERROR-011: ConfigPanel组件国际化违规 ✅
- **问题**: 80个硬编码中文文本
- **解决**: 创建config命名空间，完整i18n支持

### ERROR-012: ConfigBackupPanel组件国际化违规 ✅
- **问题**: 56个硬编码中文文本
- **解决**: 扩展config命名空间，完整i18n支持

### ERROR-013: 语言切换导致UI交互失效 ✅
- **问题**: window.location.reload()导致组件失效
- **解决**: 移除强制刷新，依赖React自动重渲染

### ERROR-064: 不必要的类型断言 ✅
- **问题**: ESLint检测到不必要的类型断言
- **影响文件**:
  - `src/hooks/useTranslation.ts:14`
  - `src/hooks/useTranslationWithMarker.ts:117`
- **解决方案**: 移除不必要的`as string`类型断言
- **修复时间**: 2025-07-06
- **状态**: ✅ 已完全解决

## 📚 参考资源

### 官方文档
- [i18next官方文档](https://www.i18next.com/)
- [react-i18next官方文档](https://react.i18next.com/)
- [Ant Design国际化](https://ant.design/docs/react/i18n)

### 项目文档
- [重构报告](./i18n-refactoring-report.md)
- [完成报告](./i18n-refactoring-completion.md)
- [完成度报告](./completion-report.md)

## 🔗 相关文档

- [配置管理文档](../configuration/README.md)
- [前端文档](../frontend/README.md)
- [错误追踪](../error-tracking.md)

---

**文档版本**: v2.0
**最后更新**: 2025-07-04
**维护者**: QuickStart开发团队

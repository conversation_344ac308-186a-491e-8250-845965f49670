{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx", "outDir": "../../build/renderer", "baseUrl": ".", "paths": {"@/*": ["../*"], "@renderer/*": ["./*"], "@shared/*": ["../shared/*"]}}, "include": ["./**/*"], "exclude": ["node_modules", "../../build", "../../dist"]}
{"app": {"startup": {"success": "Application démarrée avec succès, version: {{version}}", "failed": "Échec du démarrage de l'application: {{error}}", "ready": "L'application est prête", "initializing": "Initialisation de l'application...", "shutdown": "L'application se ferme", "crashed": "L'application a planté: {{error}}"}, "window": {"created": "<PERSON>être créée avec succès: {{windowType}}", "failed": "Échec de la création de la fenêtre: {{error}}", "closed": "<PERSON><PERSON><PERSON> fermée: {{windowType}}", "minimized": "<PERSON><PERSON><PERSON> r<PERSON>", "maximized": "<PERSON><PERSON><PERSON> agra<PERSON>", "restored": "<PERSON><PERSON><PERSON> restau<PERSON>"}, "tray": {"created": "Barre d'état système créée avec succès", "failed": "Échec de la création de la barre d'état système: {{error}}", "clicked": "Barre d'état système cliquée"}}, "config": {"load": {"success": "Configuration chargée avec succès: {{configType}}", "failed": "Échec du chargement de la configuration: {{configType}}, erreur: {{error}}", "notFound": "Fichier de configuration introuvable: {{configType}}, utilisation des paramètres par défaut", "corrupted": "Fichier de configuration corrompu: {{configType}}, réinitialisé aux paramètres par défaut"}, "save": {"success": "Configuration sauvegardée avec succès: {{configType}}", "failed": "Échec de la sauvegarde de la configuration: {{configType}}, erreur: {{error}}"}, "backup": {"created": "Sauvegarde de configuration créée avec succès: {{filename}}", "failed": "Échec de la création de la sauvegarde de configuration: {{error}}", "restored": "Configuration restaurée avec succès depuis la sauvegarde: {{filename}}", "cleanup": "<PERSON><PERSON><PERSON><PERSON> {{count}} fichiers de sauvegarde expirés"}, "reset": {"success": "Configuration réinitialisée avec succès: {{configType}}", "failed": "Échec de la réinitialisation de la configuration: {{configType}}, erreur: {{error}}"}}, "db": {"connection": {"success": "Base de données connectée avec succès: {{database}}", "failed": "Échec de la connexion à la base de données: {{database}}, erreur: {{error}}", "closed": "Connexion à la base de données fermée: {{database}}", "timeout": "<PERSON><PERSON><PERSON> d'attente de connexion à la base de données: {{database}}"}, "query": {"success": "Requête de base de données réussie, durée: {{duration}}ms", "failed": "Échec de la requête de base de données: {{error}}", "slow": "Requête de base de données lente, durée: {{duration}}ms", "empty": "La requête de base de données a retourné un résultat vide"}, "migration": {"started": "Migration de base de données commencée: {{version}}", "completed": "Migration de base de données terminée: {{version}}", "failed": "Échec de la migration de base de données: {{error}}"}, "backup": {"started": "Sauvegarde de base de données commencée", "completed": "Sauvegarde de base de données terminée: {{filename}}", "failed": "Échec de la sauvegarde de base de données: {{error}}"}}, "file": {"add": {"success": "Fichier ajouté avec succès: {{filename}}", "failed": "Échec de l'ajout du fichier: {{filename}}, erreur: {{error}}", "duplicate": "Le fichier existe déjà: {{filename}}", "invalid": "<PERSON><PERSON><PERSON> de <PERSON> invalide: {{filename}}"}, "remove": {"success": "Fichier supprimé avec succès: {{filename}}", "failed": "Échec de la suppression du fichier: {{filename}}, erreur: {{error}}", "notFound": "Fichier introuvable: {{filename}}"}, "launch": {"success": "<PERSON><PERSON><PERSON> lancé avec succès: {{filename}}, du<PERSON>e: {{duration}}ms", "failed": "Échec du lancement du fichier: {{filename}}, erreur: {{error}}", "adminRequired": "Le fichier nécessite des privilèges administrateur pour être lancé: {{filename}}", "timeout": "<PERSON><PERSON><PERSON> d'attente du lancement du fichier: {{filename}}"}, "scan": {"started": "Analy<PERSON> <PERSON> commencée: {{directory}}", "completed": "Analy<PERSON> de fichi<PERSON> terminée, {{count}} fichiers trouvés", "failed": "Échec de l'analyse de fichiers: {{error}}"}}, "ui": {"component": {"mounted": "Composant monté: {{component}}", "unmounted": "Composant démonté: {{component}}", "rendered": "Composant rendu: {{component}}, durée: {{duration}}ms", "error": "<PERSON><PERSON><PERSON> de rendu du composant: {{component}}, erreur: {{error}}"}, "theme": {"changed": "Thème changé avec succès: {{theme}}", "failed": "Échec du changement de thème: {{error}}", "loaded": "Thème chargé avec succès: {{theme}}", "reset": "Thème réinitialisé par défaut"}, "navigation": {"changed": "Navigation de page: {{from}} -> {{to}}", "failed": "Échec de la navigation de page: {{error}}"}}, "i18n": {"language": {"changed": "Langue changée avec succès: {{from}} -> {{to}}", "failed": "Échec du changement de langue: {{error}}", "detected": "Langue système détectée: {{language}}", "fallback": "Utilisation de la langue de secours: {{language}}", "loading_saved": "Chargement de la langue sauvegardée: {{language}}", "load_failed": "Échec du chargement de la langue sauvegardée, utilisation par défaut: {{error}}"}, "translation": {"loaded": "Fichier de traduction chargé avec succès: {{language}}", "failed": "Échec du chargement du fichier de traduction: {{language}}, erreur: {{error}}", "missing": "Clé de traduction manquante: {{key}}", "cached": "Traduction mise en cache: {{language}}", "file_missing": "Fichier de traduction manquant: {{filePath}}", "file_load_failed": "Échec du chargement du fichier de traduction {{language}}/{{namespace}}: {{error}}", "resources_reload_failed": "Échec du rechargement des ressources de traduction: {{error}}"}}, "ipc": {"message": {"sent": "Message IPC envoyé: {{channel}}", "received": "Message IPC reçu: {{channel}}", "failed": "Échec du traitement du message IPC: {{channel}}, erreur: {{error}}", "timeout": "<PERSON><PERSON><PERSON> d'attente du message IPC: {{channel}}"}, "handler": {"registered": "Gestionnaire IPC enregistré: {{channel}}", "unregistered": "Gestionnaire IPC désenregistré: {{channel}}", "error": "Erreur du gestionnaire IPC: {{channel}}, erreur: {{error}}"}}, "perf": {"startup": {"time": "Temps de démarrage de l'application: {{duration}}ms", "slow": "Démarrage lent de l'application: {{duration}}ms"}, "memory": {"usage": "Utilisation mémoire: {{used}}MB / {{total}}MB ({{percentage}}%)", "high": "Utilisation mémoire élevée: {{used}}MB", "gc": "Collecte des déchets terminée, libéré: {{freed}}MB"}, "cpu": {"usage": "Utilisation CPU: {{percentage}}%", "high": "Utilisation CPU élevée: {{percentage}}%"}, "operation": {"completed": "Opération terminée: {{operation}}, durée: {{duration}}ms", "slow": "Opération lente: {{operation}}, durée: {{duration}}ms"}}, "theme": {"load": {"success": "Thème chargé avec succès: {{theme}}", "failed": "Échec du chargement du thème: {{theme}}, erreur: {{error}}"}, "apply": {"success": "Thème appliqué avec succès: {{theme}}", "failed": "Échec de l'application du thème: {{theme}}, erreur: {{error}}"}, "export": {"success": "Thème exporté avec succès: {{filename}}", "failed": "Échec de l'exportation du thème: {{error}}"}, "import": {"success": "Thème importé avec succès: {{filename}}", "failed": "Échec de l'importation du thème: {{filename}}, erreur: {{error}}"}}, "backup": {"create": {"success": "Sauvegarde créée avec succès: {{filename}}", "failed": "Échec de la création de la sauvegarde: {{error}}", "started": "Début de la création de la sauvegarde..."}, "restore": {"success": "Sauvegarde restaurée avec succès: {{filename}}", "failed": "Échec de la restauration de la sauvegarde: {{filename}}, erreur: {{error}}", "started": "Début de la restauration de la sauvegarde: {{filename}}"}, "cleanup": {"success": "Nettoyage des sauvegardes terminé, {{count}} fichiers supprimés", "failed": "Échec du nettoyage des sauvegardes: {{error}}"}}, "system": {"error": {"uncaught": "Exception non capturée: {{error}}", "unhandled": "Rejet de promesse non géré: {{reason}}", "critical": "Erreur système critique: {{error}}", "translation_failed": "Échec de la traduction du message de journal: {{key}}", "log_manager_internal": "Erreur interne du gestionnaire de journaux: {{error}}", "write_log_failed": "Échec de l'écriture du journal: {{error}}", "flush_buffer_failed": "Échec du vidage du tampon pour {{filename}}: {{error}}"}, "resource": {"low_memory": "Mémoire système faible: {{available}}MB", "low_disk": "Espace disque faible: {{available}}GB", "high_cpu": "Utilisation CPU élevée: {{usage}}%"}, "info": {"renderer_loaded": "<PERSON><PERSON><PERSON> de rendu chargé", "electron_api_available": "ElectronAPI disponible: {{available}}", "react_app_rendered": "Application React rendue avec succès", "react_app_failed": "Échec du rendu de l'application React: {{error}}", "app_mounted": "Composant d'application monté", "waiting_electron_api": "En attente d'ElectronAPI...", "electron_api_ready": "ElectronAPI est prêt", "app_initialization_completed": "Initialisation de l'application terminée", "main_i18n_initialized": "MainI18n initialisé avec la langue: {{language}}", "main_i18n_failed": "Échec de l'initialisation de MainI18n: {{error}}", "main_i18n_not_initialized": "MainI18n non initialisé, retour de la valeur par défaut", "main_i18n_resources_reloaded": "Ressources MainI18n rechargées", "main_i18n_reload_failed": "Échec du rechargement des ressources MainI18n: {{error}}", "log_config_updated": "Configuration des journaux mise à jour"}, "navigation": {"changed": "Navigation de page: {{from}} -> {{to}}", "failed": "Échec de la navigation de page: {{error}}"}}, "logs_panel": {"error": {"load_failed": "Échec du chargement des journaux", "export_failed": "Échec de l'exportation des journaux"}, "info": {"system_ready": "Système de journalisation prêt", "logs_loaded": "{{count}} entrées de journal chargées", "export_success": "Journaux exportés avec succès"}, "actions": {"refresh": "Actualiser les journaux", "export": "Exporter les journaux"}, "ui": {"recent_logs": "Journaux récents"}}}
# 日志国际化专项文档

## 📖 概述

本文档专门描述QuickStart项目日志系统的国际化实现方案，包括日志消息的多语言支持、格式化和本地化策略。

## ✅ 实现状态

**完成度**: 100% ✅
**最后验证**: 2025-07-05
**状态**: 已完成，生产环境验证通过

## 🌍 国际化策略

### 支持语言
- **zh-CN**: 简体中文
- **en**: 英语
- **ru**: 俄语
- **fr**: 法语

### 日志消息分类
```
日志国际化
├── 系统消息 (system)
├── 错误消息 (errors)
├── 用户操作 (user-actions)
├── 性能监控 (performance)
└── 调试信息 (debug)
```

## 📝 日志消息结构设计

### 消息键命名规范
```
logs.{category}.{subcategory}.{action}.{status}

示例:
- logs.app.startup.success
- logs.file.add.failed
- logs.config.load.success
- logs.user.language.changed
```

### 消息模板格式
```typescript
interface LogMessage {
  key: string;           // 消息键
  level: LogLevel;       // 日志级别
  template: string;      // 消息模板
  params?: string[];     // 参数列表
  context?: any;         // 上下文信息
}
```

## 🗂️ 日志消息资源文件

### 1. 系统消息 (system.json)

#### 简体中文 (zh-CN)
```json
{
  "app": {
    "startup": {
      "begin": "正在启动应用程序...",
      "success": "应用程序启动成功",
      "failed": "应用程序启动失败: {{error}}"
    },
    "shutdown": {
      "begin": "正在关闭应用程序...",
      "success": "应用程序已安全关闭",
      "forced": "应用程序被强制关闭"
    },
    "window": {
      "created": "主窗口已创建",
      "ready": "窗口已就绪并显示",
      "closed": "窗口已关闭"
    }
  },
  "database": {
    "init": {
      "begin": "正在初始化数据库...",
      "success": "数据库初始化完成",
      "failed": "数据库初始化失败: {{error}}"
    },
    "connection": {
      "established": "数据库连接已建立",
      "lost": "数据库连接丢失",
      "restored": "数据库连接已恢复"
    }
  },
  "config": {
    "load": {
      "begin": "正在加载配置文件...",
      "success": "配置文件加载成功: {{configType}}",
      "failed": "配置文件加载失败: {{configType}} - {{error}}"
    },
    "save": {
      "success": "配置文件保存成功: {{configType}}",
      "failed": "配置文件保存失败: {{configType}} - {{error}}"
    },
    "backup": {
      "created": "配置备份已创建: {{backupPath}}",
      "failed": "配置备份创建失败: {{error}}"
    }
  }
}
```

#### 英语 (en-US)
```json
{
  "app": {
    "startup": {
      "begin": "Starting application...",
      "success": "Application started successfully",
      "failed": "Application startup failed: {{error}}"
    },
    "shutdown": {
      "begin": "Shutting down application...",
      "success": "Application closed safely",
      "forced": "Application was forced to close"
    },
    "window": {
      "created": "Main window created",
      "ready": "Window is ready and visible",
      "closed": "Window closed"
    }
  },
  "database": {
    "init": {
      "begin": "Initializing database...",
      "success": "Database initialization completed",
      "failed": "Database initialization failed: {{error}}"
    },
    "connection": {
      "established": "Database connection established",
      "lost": "Database connection lost",
      "restored": "Database connection restored"
    }
  },
  "config": {
    "load": {
      "begin": "Loading configuration files...",
      "success": "Configuration loaded successfully: {{configType}}",
      "failed": "Configuration load failed: {{configType}} - {{error}}"
    },
    "save": {
      "success": "Configuration saved successfully: {{configType}}",
      "failed": "Configuration save failed: {{configType}} - {{error}}"
    },
    "backup": {
      "created": "Configuration backup created: {{backupPath}}",
      "failed": "Configuration backup failed: {{error}}"
    }
  }
}
```

### 2. 用户操作消息 (user-actions.json)

#### 简体中文 (zh-CN)
```json
{
  "file": {
    "add": {
      "success": "用户添加文件: {{filename}}",
      "failed": "用户添加文件失败: {{filename}} - {{error}}",
      "batch": "用户批量添加 {{count}} 个文件"
    },
    "remove": {
      "success": "用户删除文件: {{filename}}",
      "failed": "用户删除文件失败: {{filename}} - {{error}}"
    },
    "launch": {
      "success": "用户启动文件: {{filename}}",
      "failed": "用户启动文件失败: {{filename}} - {{error}}"
    }
  },
  "theme": {
    "change": {
      "color": "用户修改主题颜色: {{oldColor}} -> {{newColor}}",
      "mode": "用户切换主题模式: {{oldMode}} -> {{newMode}}",
      "font": "用户修改字体: {{oldFont}} -> {{newFont}}"
    }
  },
  "language": {
    "change": "用户切换语言: {{oldLang}} -> {{newLang}}"
  },
  "window": {
    "minimize": "用户最小化窗口",
    "maximize": "用户最大化窗口",
    "restore": "用户恢复窗口",
    "close": "用户关闭窗口"
  }
}
```

### 3. 错误消息 (errors.json)

#### 简体中文 (zh-CN)
```json
{
  "file": {
    "notFound": "文件不存在: {{filePath}}",
    "permissionDenied": "文件权限不足: {{filePath}}",
    "invalidPath": "无效的文件路径: {{filePath}}",
    "corruptedFile": "文件已损坏: {{filePath}}"
  },
  "database": {
    "connectionFailed": "数据库连接失败: {{error}}",
    "queryFailed": "数据库查询失败: {{query}} - {{error}}",
    "transactionFailed": "数据库事务失败: {{error}}"
  },
  "config": {
    "parseError": "配置文件解析错误: {{configType}} - {{error}}",
    "validationError": "配置验证失败: {{configType}} - {{field}}",
    "backupError": "配置备份失败: {{error}}"
  },
  "ipc": {
    "handlerNotFound": "IPC处理器未找到: {{channel}}",
    "invalidParameters": "IPC参数无效: {{channel}} - {{params}}",
    "timeout": "IPC调用超时: {{channel}}"
  }
}
```

### 4. 性能监控消息 (performance.json)

#### 简体中文 (zh-CN)
```json
{
  "timing": {
    "operation": "操作耗时: {{operation}} - {{duration}}ms",
    "startup": "应用启动耗时: {{duration}}ms",
    "shutdown": "应用关闭耗时: {{duration}}ms"
  },
  "memory": {
    "usage": "内存使用: RSS={{rss}}MB, Heap={{heap}}MB",
    "warning": "内存使用警告: 当前{{current}}MB, 阈值{{threshold}}MB",
    "gc": "垃圾回收执行: 回收{{freed}}MB, 耗时{{duration}}ms"
  },
  "database": {
    "queryTime": "数据库查询耗时: {{query}} - {{duration}}ms",
    "slowQuery": "慢查询警告: {{query}} - {{duration}}ms"
  }
}
```

## 🔧 国际化实现方案

### 1. 日志国际化管理器
```typescript
export class LogI18nManager {
  private static instance: LogI18nManager;
  private currentLanguage: string = 'zh-CN';
  private messages: Map<string, any> = new Map();
  
  static getInstance(): LogI18nManager {
    if (!this.instance) {
      this.instance = new LogI18nManager();
    }
    return this.instance;
  }
  
  async loadMessages(language: string): Promise<void> {
    const categories = ['system', 'user-actions', 'errors', 'performance'];
    
    for (const category of categories) {
      const messagesPath = path.join(
        __dirname,
        '../i18n/locales',
        language,
        'logs',
        `${category}.json`
      );
      
      try {
        const messages = await fs.readJson(messagesPath);
        this.messages.set(`${language}.${category}`, messages);
      } catch (error) {
        console.error(`Failed to load log messages: ${language}.${category}`, error);
      }
    }
  }
  
  getMessage(key: string, params?: Record<string, any>): string {
    const [category, ...keyParts] = key.split('.');
    const messageKey = keyParts.join('.');
    
    const messages = this.messages.get(`${this.currentLanguage}.${category}`);
    if (!messages) {
      return key; // 回退到键名
    }
    
    let message = this.getNestedValue(messages, messageKey);
    if (!message) {
      return key; // 回退到键名
    }
    
    // 参数替换
    if (params) {
      message = this.interpolateParams(message, params);
    }
    
    return message;
  }
  
  private getNestedValue(obj: any, path: string): string | null {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null;
    }, obj);
  }
  
  private interpolateParams(template: string, params: Record<string, any>): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return params[key] !== undefined ? String(params[key]) : match;
    });
  }
  
  setLanguage(language: string): void {
    this.currentLanguage = language;
  }
}
```

### 2. 国际化日志记录器
```typescript
export class I18nLogger {
  private logger: Logger;
  private i18nManager: LogI18nManager;
  
  constructor() {
    this.logger = Logger.getInstance();
    this.i18nManager = LogI18nManager.getInstance();
  }
  
  info(messageKey: string, params?: Record<string, any>, context?: any): void {
    const message = this.i18nManager.getMessage(messageKey, params);
    this.logger.info(message, context);
  }
  
  error(messageKey: string, params?: Record<string, any>, error?: Error): void {
    const message = this.i18nManager.getMessage(messageKey, params);
    this.logger.error(message, error);
  }
  
  warn(messageKey: string, params?: Record<string, any>, context?: any): void {
    const message = this.i18nManager.getMessage(messageKey, params);
    this.logger.warn(message, context);
  }
  
  debug(messageKey: string, params?: Record<string, any>, context?: any): void {
    const message = this.i18nManager.getMessage(messageKey, params);
    this.logger.debug(message, context);
  }
  
  // 性能监控专用方法
  performance(operation: string, duration: number): void {
    this.info('performance.timing.operation', {
      operation,
      duration: duration.toFixed(2)
    });
  }
  
  // 用户操作专用方法
  userAction(action: string, params?: Record<string, any>): void {
    this.info(`user-actions.${action}`, params);
  }
}
```

### 3. 使用示例
```typescript
// 初始化国际化日志系统
const i18nLogger = new I18nLogger();
const i18nManager = LogI18nManager.getInstance();

// 加载当前语言的日志消息
await i18nManager.loadMessages('zh-CN');
i18nManager.setLanguage('zh-CN');

// 使用国际化日志
i18nLogger.info('system.app.startup.success');
i18nLogger.error('errors.file.notFound', { filePath: '/path/to/file.exe' });
i18nLogger.userAction('file.add.success', { filename: 'chrome.exe' });
i18nLogger.performance('file-launch', 150.5);

// 语言切换
await i18nManager.loadMessages('en-US');
i18nManager.setLanguage('en-US');
```

## 📊 实施进度

### 已完成 ✅
- ✅ 国际化方案设计
- ✅ 消息键命名规范
- ✅ 资源文件结构设计
- ✅ 日志消息资源文件编写 (100%)
- ✅ 国际化管理器实现 (100%)
- ✅ I18nLogger类实现
- ✅ 与现有日志系统集成
- ✅ 多语言消息文件完善
- ✅ 生产环境验证
- ✅ 文档完善

## 🔍 已知问题

### 已解决问题
- ✅ **基础日志系统**: 已完成实现
- ✅ **消息文件**: 完整的4种语言消息文件
- ✅ **集成方案**: 成功与现有代码集成
- ✅ **循环依赖**: 避免了i18n系统的循环依赖问题

### 当前状态
- **状态**: 🎉 **完全正常** - 所有功能正常工作
- **问题数量**: 0个未解决问题
- **稳定性**: 优秀 - 生产环境稳定运行

## 🎉 实施成果

### 完成的功能
1. ✅ **基础日志系统**: 完整的日志系统实现
2. ✅ **国际化管理器**: LogI18nManager类完全实现
3. ✅ **消息资源文件**: 4种语言的完整消息文件
4. ✅ **系统集成**: 成功集成到现有代码中
5. ✅ **生产验证**: 通过生产环境测试和优化

### 技术亮点
- **完整的国际化支持**: 4种语言的日志消息翻译
- **智能参数替换**: 支持动态参数插值
- **性能优化**: 高效的消息查找和缓存机制
- **错误处理**: 优雅的回退机制和错误处理

## 🔗 相关文档

- [日志系统主文档](./README.md)
- [国际化文档](../i18n/README.md)
- [错误追踪](../error-tracking.md)

---

**日志国际化系统实现完全成功！** 🚀✨

*最后更新: 2025-07-05*
*文档版本: 2.0*

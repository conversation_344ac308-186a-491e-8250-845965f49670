---
description: 所有需要记录运行状态、错误信息、用户操作的场景时使用
globs: 
alwaysApply: false
---
# 日志系统使用规范

## 使用场景
- 所有需要记录运行状态、错误信息、用户操作的场景
- 调试和故障排查
- 性能监控和分析
- 用户行为追踪

## 关键规则
- **优先使用国际化日志方法**：使用 `logger.errorI18n`、`logger.infoI18n` 等代替普通方法
- 根据进程类型选择正确的日志记录器
- 按照重要性选择合适的日志级别
- 使用准确的来源分类标识日志类型
- 提供有意义的元数据和上下文信息
- 避免在循环中频繁记录日志
- 错误日志必须包含错误对象和上下文
- 所有错误日志必须使用翻译键，格式为 `errors.category.specific_error`

## 日志级别选择

### debug (优先级0)
- 详细的调试信息，仅在开发时使用
- 函数进入/退出、变量值、执行路径
- 生产环境默认不输出

### info (优先级1，默认级别)
- 一般信息，如功能执行、状态变更
- 应用启动/关闭、配置加载、用户登录
- 正常的业务流程记录

### warn (优先级2)
- 警告信息，如配置问题、性能问题
- 可恢复的错误、降级处理
- 潜在的问题但不影响主要功能

### error (优先级3)
- 错误信息，如异常、失败操作
- 不可恢复的错误、系统故障
- 需要立即关注的问题

## 来源分类

- **APP** - 应用程序级别：应用启动/关闭、核心业务逻辑
- **USER** - 用户操作：用户交互、表单提交、用户行为追踪
- **SYS** - 系统级别：系统资源、文件操作、进程管理
- **API** - API调用：HTTP请求/响应、外部服务调用
- **I18N** - 国际化：语言切换、翻译加载
- **DB** - 数据库操作：数据库连接、查询执行
- **UI** - 用户界面：组件渲染、界面交互

## 使用示例

### 主进程日志记录
```typescript
import { logger } from '../shared/logger/LogManager';

// 应用启动
await logger.info('Application started successfully', 'APP', 'main.ts', {
  version: app.getVersion(),
  platform: process.platform
});

// 系统错误（使用国际化）
await logger.errorI18n('errors.system.window_creation_failed', 'Failed to create window', 'SYS', 'main.ts', {}, {
  windowType: 'main',
  screenSize: screen.getPrimaryDisplay().size
}, error);
```

### 渲染进程日志记录
```typescript
import { rendererLogger } from '../shared/logger/RendererLogger';

// 组件生命周期
await rendererLogger.info('Component mounted', 'UI', 'App.tsx', {
  component: 'MainApp'
});

// API调用
await rendererLogger.debug('API request started', 'API', 'api.ts', {
  method: 'POST',
  url: '/api/users'
});

// 错误处理（使用国际化）
await rendererLogger.errorI18n('errors.network.request_failed', 'Network request failed', 'API', 'api.ts', {}, {
  url: endpoint,
  method: 'GET'
}, networkError);
```

### 国际化日志记录（推荐使用）
```typescript
// ✅ 主进程国际化日志（推荐）
await logger.infoI18n('logger.config.level_changed', 'Log level changed to {{level}}', 'SYS', 'main.ts', {
  level: newLevel
});

await logger.errorI18n('errors.database.connection_failed', 'Database connection failed', 'DB', 'database.ts', {}, {
  host: dbConfig.host,
  port: dbConfig.port
}, error);

// ✅ 渲染进程国际化日志（推荐）
await rendererLogger.infoI18n('logger.language.changed', 'Language changed to {{language}}', 'I18N', 'App.tsx', {
  language: newLanguage
});

// ❌ 避免使用普通日志方法
// await logger.error('Database connection failed', 'DB', 'database.ts', metadata, error);
```

## 翻译键规范
- **错误日志**：`errors.category.specific_error`
  - `errors.network.*` - 网络相关错误
  - `errors.database.*` - 数据库错误  
  - `errors.file.*` - 文件操作错误
  - `errors.application.*` - 应用级错误
  - `errors.system.*` - 系统错误
  - `errors.ui.*` - 界面错误

- **普通日志**：`logger.category.action`
  - `logger.config.*` - 配置相关
  - `logger.language.*` - 语言相关
  - `logger.file.*` - 文件操作
  - `logger.ipc.*` - 进程间通信

## 性能优化

### 缓冲机制
- 渲染进程：50条缓冲，2秒自动刷新
- 主进程：100条缓冲，5秒自动刷新
- 错误日志立即刷新，不使用缓冲

### 避免性能问题
```typescript
// ❌ 错误：在循环中频繁记录
for (let i = 0; i < 1000; i++) {
  await rendererLogger.debug(`Processing item ${i}`, 'APP', 'processor.ts');
}

// ✅ 正确：批量记录或降低频率
await rendererLogger.info('Starting batch processing', 'APP', 'processor.ts', {
  totalItems: items.length
});

// 只在关键点记录日志
if (i % 100 === 0) {
  await rendererLogger.debug(`Processed ${i} items`, 'APP', 'processor.ts');
}
```

## 最佳实践
- 优先使用国际化日志方法
- 错误日志必须包含完整上下文
- 避免在高频循环中记录日志
- 使用合适的日志级别
- 提供有意义的元数据




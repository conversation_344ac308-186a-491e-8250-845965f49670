/**
 * QuickStart 项目配置
 * 集中管理项目相关的配置信息
 */

export interface ProjectConfig {
  name: string;
  version: string;
  description: string;
  author: {
    name: string;
    email: string;
  };
  repository: {
    type: string;
    url: string;
  };
  links: {
    homepage: string;
    repository: string;
    issues: string;
    documentation: string;
    support: string;
    sponsors?: string;
  };
  license: {
    type: string;
    url: string;
  };
}

// 项目配置常量
export const PROJECT_CONFIG: ProjectConfig = {
  name: 'QuickStart',
  version: '1.0.0',
  description: 'QuickStart - 强大的文件快速启动工具，采用TypeScript + React + Electron架构，集成Ant Design UI框架和Apple风格设计',
  author: {
    name: 'QuickStart Team',
    email: '<EMAIL>'
  },
  repository: {
    type: 'git',
    url: 'https://github.com/quickstart-team/quickstart'
  },
  links: {
    homepage: 'https://quickstart.app',
    repository: 'https://github.com/quickstart-team/quickstart',
    issues: 'https://github.com/quickstart-team/quickstart/issues',
    documentation: 'https://docs.quickstart.app',
    support: 'https://support.quickstart.app',
    sponsors: 'https://github.com/sponsors/quickstart-team'
  },
  license: {
    type: 'MIT',
    url: 'https://github.com/quickstart-team/quickstart/blob/main/LICENSE'
  }
} as const;

// 导出便捷访问的常量
export const GITHUB_LINKS = {
  repo: PROJECT_CONFIG.links.repository,
  issues: PROJECT_CONFIG.links.issues,
  sponsors: PROJECT_CONFIG.links.sponsors
} as const;

// 版本信息
export const VERSION_INFO = {
  app: PROJECT_CONFIG.version,
  // 这些版本信息将通过Electron API动态获取
  electron: process.versions?.electron || 'Unknown',
  node: process.versions?.node || 'Unknown',
  chrome: process.versions?.chrome || 'Unknown'
} as const;

{"title": "Theme Settings", "mode": {"light": "Light Mode", "dark": "Dark Mode", "auto": "Follow System", "toggle": "Toggle Theme Mode"}, "presets": {"title": "Theme Presets", "iosBlue": "iOS Blue", "macosGreen": "macOS Green", "appleOrange": "Apple Orange", "deepSpaceGray": "Deep Space Gray", "midnightBlue": "Midnight Blue", "custom": "Custom Theme"}, "colors": {"title": "Interface Colors", "primary": "Primary Color", "secondary": "Secondary Color", "background": "Background Color", "surface": "Surface Color", "text": "Text Color", "textSecondary": "Secondary Text Color", "border": "Border Color", "success": "Success Color", "warning": "Warning Color", "error": "Error Color", "info": "Info Color", "accent": "Accent Color", "muted": "Muted Color", "highlight": "Highlight Color", "shadow": "Shadow Color", "interface": "Interface Colors", "labels": {"surface": "Surface Color", "text": "Text Color", "textSecondary": "Secondary Text", "border": "Border Color", "shadow": "Shadow Color"}, "descriptions": {"surface": "Background color for cards and panels", "text": "Primary text color", "textSecondary": "Secondary text and description color", "border": "Border and divider color", "shadow": "Shadow and drop shadow color"}}, "fonts": {"title": "Font Settings", "family": "Font Family", "size": "Font Size", "weight": "Font Weight", "lineHeight": "Line Height", "letterSpacing": "Letter Spacing", "systemFont": "System Font", "customFont": "Custom Font", "configuration": "Font Configuration", "options": {"appleSystem": "Apple System Font", "microsoftYaHei": "Microsoft YaHei", "pingFangSC": "PingFang SC", "helveticaNeue": "Helvetica Neue", "sfProDisplay": "SF Pro Display", "segoeUI": "Segoe UI"}, "weights": {"thin": "Thin", "light": "Light", "regular": "Regular", "medium": "Medium", "semibold": "Semibold", "bold": "Bold", "heavy": "Heavy"}}, "effects": {"title": "Visual Effects", "glassEffect": "Glass Effect", "transparency": "Transparency", "blur": "Blur", "shadows": "Shadow Effects", "animations": "Animation Effects", "transitions": "Transition Effects", "borderRadius": "Border Radius", "gradient": "Gradient Effects", "compactMode": "Compact Mode"}, "layout": {"title": "Layout Settings", "sidebar": "Sidebar", "sidebarWidth": "<PERSON>bar Width", "sidebarPosition": "Sidebar Position"}, "background": {"title": "Background Settings", "type": "Background Type", "color": "Solid Color Background", "gradient": "Gradient Background", "image": "Image Background", "pattern": "Pattern Background", "opacity": "Background Opacity", "position": "Background Position", "size": "Background Size", "repeat": "Background Repeat", "attachment": "Background Attachment", "blend": "Blend Mode", "enabled": "Enable Background", "basicSettings": "Basic Settings", "colorSettings": "Color Settings", "gradientSettings": "<PERSON><PERSON><PERSON>s", "imageSettings": "Image Settings", "reset": "Reset Settings", "gradientType": "Gradient Type", "gradientDirection": "Gradient Direction", "gradientColors": "Gradient Colors", "startColor": "Start", "endColor": "End", "colorStop": "Color", "addColor": "Add Color", "removeColor": "Remove Color", "noGradientColors": "No gradient colors", "clickAddColor": "Click \"Add Color\" button to add gradient colors", "gradientTypes": {"linear": "Linear Gradient", "radial": "<PERSON><PERSON>"}, "types": {"none": "No Background", "color": "Solid Color", "gradient": "Gradient", "image": "Image"}, "performanceSettings": "Performance Settings", "performanceTip": "Performance Tip", "performanceDesc": "High-resolution background images may affect application performance. It is recommended to use appropriate compression settings.", "localImage": "Local Image", "networkImage": "Network Image", "dragOrClick": "Click or drag image to this area to upload", "supportFormats": "Supports JPG, PNG, GIF, WebP formats, maximum 50MB", "compressionTip": "Compression Tip", "compressionDesc": "Large images will be automatically compressed to optimize performance while maintaining good visual effects.", "displayMode": "Display Mode", "blur": "Blur", "brightness": "Brightness", "contrast": "Contrast", "saturation": "Saturation", "modes": {"stretch": "<PERSON><PERSON><PERSON>", "tile": "Tile", "center": "Center", "cover": "Cover", "contain": "Contain"}, "processing": "Processing image...", "urlLoadSuccess": "Network image loaded successfully", "urlLoadFailed": "Failed to load network image", "removeSuccess": "Background image removed", "preview": "Preview", "viewFullSize": "View full size", "removeImage": "Remove image", "loadError": "Failed to load background configuration", "noConfig": "Background configuration not available", "configWillBeCreated": "Configuration will be created on first use", "urlImage": "Network Image", "enterImageUrl": "Enter image URL (https://...)", "urlTip": "Network Image Tip", "urlDesc": "Please ensure the image link is accessible and supports HTTPS protocol.", "enterUrl": "Please enter image URL", "uploadSuccess": "Background image uploaded successfully", "uploadFailed": "Background image upload failed", "downloadingImage": "Downloading network image...", "processingLocalImage": "Processing local image...", "compressingImage": "Compressing image to optimize performance...", "savingImage": "Saving background image...", "imageSetSuccess": "Background image set successfully", "imageSetFailed": "Failed to set background image", "usingMemoryMode": "Using memory mode to set background (needs to be reset after restart)", "imageSetSuccessMemory": "Background image set successfully (temporary mode)", "imageSetFailedComplete": "Background image setting completely failed", "defaultImageName": "Background Image", "duplicateFileName": "Duplicate File Name", "duplicateFileContent": "File name \"{{fileName}}\" already exists, rename and save?", "renameAndSave": "Rename and Save", "cancelSave": "Cancel saving background image", "permissionDeniedError": "Insufficient cache directory permissions, please check application permissions", "diskSpaceError": "Insufficient disk space, cannot save background image", "cacheNotInitializedError": "Cache system not initialized, retrying...", "imageCacheFailedError": "Image cache failed: {{message}}", "imageValidation": {"unsupportedFormat": "Unsupported image format: {{format}}. Supported formats: JPEG, PNG, GIF, WebP", "fileTooLarge": "Image file too large: {{size}}MB. Maximum supported: 50MB", "fileInvalid": "Invalid file object", "emptyFile": "File size is 0, possibly an empty file", "invalidFileType": "Invalid file type: {{type}}, expected image type", "loadTimeout": "Image load timeout (5 seconds), file may be corrupted or too large", "readTimeout": "File read timeout (10 seconds), file may be too large or corrupted", "invalidDimensions": "Invalid image dimensions, file may be corrupted or not a valid image format", "cannotReadFile": "Cannot read image file", "jpegCorrupted": "JPEG file exists but may be corrupted, please try resaving with image editing software", "pngCorrupted": "PNG file exists but may be corrupted, please try resaving with image editing software", "gifCorrupted": "GIF file exists but may be corrupted, please try resaving with image editing software", "webpCorrupted": "WebP file exists but may be corrupted or not supported in current environment", "bmpCorrupted": "BMP file exists but may be corrupted, recommend converting to JPG or PNG format", "webpNotSupported": "WebP format not supported in current browser, please use JPG or PNG format", "fileCorruptedGeneric": "File is not a valid image format, or is corrupted. File header: {{header}}...", "unsupportedFileType": "File type not supported: {{type}}. Please select a valid image file (JPG, PNG, GIF, WebP)", "fileTooLargeSimple": "Image file too large, please select an image smaller than 50MB", "corruptionSuggestions": "Possible causes: file may be corrupted; image format may not be supported; file permissions may be problematic"}, "imageProcessing": {"timeout": "Image processing timeout (15 seconds), image may be too large", "cannotCreateCanvas": "Cannot create Canvas context", "cannotGeneratePreview": "Cannot generate image preview", "processingFailed": "Image processing failed: {{message}}", "cannotLoadForProcessing": "Cannot load image for processing", "batchProcessingFailed": "Failed to process image {{fileName}}", "setImageSourceFailed": "Failed to set image source: {{message}}", "fileReadFailed": "File read failed: {{message}}", "unknownError": "Unknown error"}, "imageNetwork": {"downloadTimeout": "Network image download timeout: {{seconds}} seconds", "loadTimeout": "Network image load timeout: {{seconds}} seconds", "corsError": "CORS error: Cannot access this image, server does not allow cross-origin requests", "networkError": "Network error: Please check network connection and image link validity", "downloadFailed": "Failed to download network image: {{message}}", "httpError": "HTTP {{status}}: {{statusText}}", "invalidContentType": "Invalid content type: {{contentType}}. Expected image type.", "invalidUrl": "Invalid image link format", "protocolNotSupported": "Only HTTP and HTTPS protocol image links are supported", "invalidExtension": "Image link must end with supported format (.jpg, .jpeg, .png, .gif, .webp)", "downloadTimeoutRetry": "Network image download timeout, please check network connection or try again later", "corsAccessDenied": "Cannot access this image, server does not allow cross-origin requests", "networkConnectionError": "Network connection error, please check network settings", "serverError": "Server error: {{message}}", "fileTooLargeNetwork": "Network image file too large", "invalidImageLink": "Link is not a valid image file", "genericError": "{{baseMessage}}: {{details}}"}, "cache": {"initializationFailed": "Cache manager initialization failed: {{message}}", "permissionDeniedCreate": "Permission denied: Cannot create or write to cache directory: {{dir}}. Please check folder permissions.", "insufficientDiskSpaceCreate": "Insufficient disk space to create cache directory: {{dir}}", "invalidPathCreate": "Invalid path: A file exists where directory should be created: {{dir}}", "ensureCacheDirectoryFailed": "Failed to ensure cache directory: {{message}}", "insufficientDiskSpaceCache": "Insufficient disk space: required {{required}}MB, available {{available}}MB", "fileWriteVerificationFailed": "File write verification failed: expected {{expected}} bytes, got {{actual}} bytes", "insufficientDiskSpace": "Insufficient disk space to cache image", "permissionDeniedWrite": "Permission denied: Cannot write to cache directory: {{dir}}", "tooManyOpenFiles": "Too many open files: Cannot cache image", "cacheImageFailed": "Failed to cache image: {{message}}", "cleanupPartialFileFailed": "Failed to cleanup partial file"}, "cachedImages": "Cached Images", "noCachedImages": "No Cached Images", "cachedImagesDesc": "Click image to switch background, click delete button to remove from cache", "switchToImage": "Switch to This Background", "removeFromCache": "Remove from Cache", "confirmRemoveCache": "Confirm Re<PERSON><PERSON>", "confirmRemoveCacheContent": "Are you sure to remove image \"{{fileName}}\" from cache? This action cannot be undone.", "removeCacheSuccess": "Image removed from cache", "removeCacheFailed": "Failed to remove cached image", "imageCache": "Image Cache", "cacheManagement": "Cache Management", "customFileName": "Custom File Name", "enterCustomFileName": "Please enter custom file name", "fileNameInvalid": "Invalid file name", "renameImage": "Rename Image", "newFileName": "New File Name", "renameSuccess": "Rename successful", "renameFailed": "<PERSON><PERSON> failed", "enterNewFileName": "Please enter new file name"}, "actions": {"apply": "Apply Theme", "reset": "Reset Theme", "customize": "Customize Theme", "duplicate": "Duplicate Theme", "rename": "<PERSON><PERSON>", "share": "Share Theme"}, "messages": {"themeApplied": "Theme applied", "themeReset": "Theme reset", "themeSaved": "Theme saved", "themeLoaded": "Theme loaded", "themeExported": "Theme configuration exported", "themeImported": "Theme configuration imported", "themeImportError": "Theme configuration file format error", "layoutUpdated": "Layout settings updated", "layoutUpdateFailed": "Layout settings update failed", "layoutUpdateError": "Error occurred while updating layout settings", "loadingLayout": "Loading layout settings...", "loadLayoutFailed": "Failed to load layout settings", "noLayoutConfig": "Unable to load layout settings", "checkConfigSystem": "Please check if the configuration system is working properly", "invalidThemeFile": "Invalid theme file", "themeNameRequired": "Please enter theme name", "themeNameExists": "Theme name already exists", "confirmDeleteTheme": "Confirm to delete this theme?", "confirmResetTheme": "Confirm to reset theme settings?", "themeDeleteConfirm": "Confirm to delete this theme?", "themeResetConfirm": "Confirm to reset theme to default?", "customThemeNameRequired": "Custom theme name is required", "customThemeNameExists": "Custom theme name already exists", "themeFileInvalid": "Invalid theme file format", "themeLoadError": "Theme load error", "themeSaveError": "Theme save error", "previewMode": "Preview mode enabled", "previewModeDisabled": "Preview mode disabled", "colorPickerTitle": "Select Color", "resetToDefault": "Reset to default", "importTheme": "Import Theme", "exportTheme": "Export Theme", "themePreview": "Theme Preview", "applyChanges": "Apply Changes", "discardChanges": "Discard Changes", "unsavedChanges": "You have unsaved theme changes"}, "global": {"title": "Global Theme", "primaryColor": "Primary Color", "primaryColorDesc": "Affects the color of buttons, links and other primary elements", "mode": "Display Mode", "darkMode": "Dark Mode", "modeDesc": "Switch between light and dark display modes", "font": "Font Settings", "fontSize": "Font Size", "borderRadius": "Border Radius"}, "ui": {"title": "Style Settings", "description": "Customize the appearance and theme of the application", "interfaceColors": "Interface Colors", "themePresets": "Theme Presets", "typography": "Typography", "visualEffects": "Visual Effects", "layoutSettings": "Layout Settings", "primaryColorDesc": "Used for buttons, links and other primary elements", "colorLabels": {"background": "Background", "surface": "Surface", "text": "Text", "textSecondary": "Secondary Text", "border": "Border", "shadow": "Shadow"}, "fontSize": "Font Size", "interfaceEffects": "Interface Effects", "sidebar": {"title": "Sidebar Settings", "position": "Sidebar Position", "width": "<PERSON>bar Width", "collapsed": "De<PERSON>ult Collapsed", "autoHide": "Auto Hide", "positions": {"left": "Left", "right": "Right"}}, "fileList": {"title": "File List Settings", "showSize": "Show Size", "showModifiedTime": "Show Modified Time", "showAddedTime": "Show Added Time", "showLaunchCount": "Show Launch Count"}, "statusBar": {"title": "Status Bar Settings", "visible": "Show Status Bar", "showFileCount": "Show File Count", "showPath": "Show Path"}}}
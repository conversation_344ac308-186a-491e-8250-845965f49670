/**
 * Electron API 工具函数
 * 提供等待 electronAPI 准备就绪的功能
 */

/**
 * 等待 electronAPI 准备就绪
 * @param maxWaitTime 最大等待时间（毫秒），默认5000ms
 * @returns Promise<boolean> 是否成功获取到API
 */
export const waitForElectronAPI = async (maxWaitTime: number = 5000): Promise<boolean> => {
  return new Promise((resolve) => {
    // 检查API是否基本可用（降低要求）
    const isAPIReady = () => {
      const hasAPI = !!window.electronAPI;
      if (!hasAPI) return false;

      // 给一些时间让contextBridge完全初始化
      try {
        return typeof window.electronAPI === 'object';
      } catch {
        return false;
      }
    };

    if (isAPIReady()) {
      // 额外等待一点时间确保API完全初始化
      setTimeout(() => resolve(true), 50);
      return;
    }

    let attempts = 0;
    const maxAttempts = maxWaitTime / 100; // 每100ms检查一次
    const checkInterval = setInterval(() => {
      attempts++;
      if (isAPIReady()) {
        clearInterval(checkInterval);
        // 额外等待一点时间确保API完全初始化
        setTimeout(() => resolve(true), 50);
      } else if (attempts >= maxAttempts) {
        clearInterval(checkInterval);
        console.warn('ElectronAPI not ready after waiting:', {
          electronAPI: !!window.electronAPI,
          config: !!window.electronAPI?.config,
          get: !!window.electronAPI?.config?.get,
          set: !!window.electronAPI?.config?.set,
          typeof: typeof window.electronAPI
        });
        resolve(false);
      }
    }, 100);
  });
};

/**
 * 安全地调用 electronAPI 方法
 * @param apiCall API调用函数
 * @param fallbackValue 失败时的回退值
 * @param maxWaitTime 最大等待时间
 * @returns Promise<T> API调用结果或回退值
 */
export const safeElectronAPICall = async <T>(
  apiCall: () => Promise<T>,
  fallbackValue: T,
  maxWaitTime: number = 5000
): Promise<T> => {
  try {
    const apiReady = await waitForElectronAPI(maxWaitTime);
    if (!apiReady) {
      console.warn('Electron API not available after waiting');
      return fallbackValue;
    }

    // 检查API结构，但不要太严格
    if (!window.electronAPI) {
      console.warn('Electron API not found');
      return fallbackValue;
    }

    return await apiCall();
  } catch (error) {
    console.error('Electron API call failed:', error);
    return fallbackValue;
  }
};

/**
 * 检查 electronAPI 是否可用
 * @returns boolean
 */
export const isElectronAPIAvailable = (): boolean => {
  return typeof window !== 'undefined' &&
         !!window.electronAPI &&
         typeof window.electronAPI === 'object';
};

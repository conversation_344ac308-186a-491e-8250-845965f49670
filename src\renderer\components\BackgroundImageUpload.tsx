import React, { useState, useCallback, useRef } from 'react';
import {
  Upload,
  <PERSON>ton,
  Card,
  Row,
  Col,
  Typography,
  Progress,
  message,
  Space,
  Image,
  Tooltip,
  Alert,
  Input,
  Tabs,
  Spin,
} from 'antd';
import {
  UploadOutlined,
  LinkOutlined,
  DeleteOutlined,
  EyeOutlined,
  CompressOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { UploadFile, UploadProps } from 'antd';
import { ImageProcessor, type ProcessedImage, type ImageMetadata } from '../utils/imageProcessor';

const { Title, Text } = Typography;
const { Dragger } = Upload;
const { TabPane } = Tabs;

export interface BackgroundImageUploadProps {
  onImageSelect: (image: ProcessedImage) => void;
  onImageRemove: () => void;
  currentImage?: string;
  loading?: boolean;
}

export const BackgroundImageUpload: React.FC<BackgroundImageUploadProps> = ({
  onImageSelect,
  onImageRemove,
  currentImage,
  loading = false,
}) => {
  const { t } = useTranslation(['theme', 'common']);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [processing, setProcessing] = useState(false);
  const [processProgress, setProcessProgress] = useState(0);
  const [imageMetadata, setImageMetadata] = useState<ImageMetadata | null>(null);
  const [urlInput, setUrlInput] = useState('');
  const [urlLoading, setUrlLoading] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 处理文件上传
  const handleFileUpload: UploadProps['customRequest'] = useCallback(async (options: any) => {
    const { file, onSuccess, onError } = options;
    
    if (!(file instanceof File)) {
      onError?.(new Error('Invalid file'));
      return;
    }

    try {
      setProcessing(true);
      setProcessProgress(0);

      console.log('BackgroundImageUpload: Processing local file:', {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      });

      // 验证文件
      const validation = ImageProcessor.validateImageFile(file);
      if (!validation.valid) {
        message.error(validation.error);
        onError?.(new Error(validation.error));
        return;
      }

      setProcessProgress(10);
      message.info(t('theme:background.processingLocalImage', '正在处理本地图片...'));

      // 获取图片元数据
      setProcessProgress(20);
      const metadata = await ImageProcessor.getImageMetadata(file);
      setImageMetadata(metadata);

      console.log('BackgroundImageUpload: Image metadata:', metadata);

      // 检查是否需要压缩
      setProcessProgress(40);
      const shouldCompress = ImageProcessor.shouldCompress(metadata);
      let processedImage: ProcessedImage;

      if (shouldCompress) {
        message.info(t('theme:background.compressingImage', '正在压缩图片以优化性能...'));
        const compressionOptions = ImageProcessor.getRecommendedCompression(metadata);
        setProcessProgress(60);
        processedImage = await ImageProcessor.processImage(file, compressionOptions);
        
        const originalSizeMB = (metadata.size / 1024 / 1024).toFixed(2);
        const compressedSizeMB = (processedImage.metadata.size / 1024 / 1024).toFixed(2);
        
        message.success(
          `图片已优化：${originalSizeMB}MB → ${compressedSizeMB}MB`
        );
      } else {
        setProcessProgress(60);
        processedImage = await ImageProcessor.processImage(file, { quality: 0.95 });
      }

      setProcessProgress(80);
      setPreviewImage(processedImage.dataUrl);

      // 为本地文件添加原始文件名信息，保持原格式
      processedImage.metadata.originalPath = file.name;

      setProcessProgress(100);
      onImageSelect(processedImage);
      onSuccess?.(processedImage);

      console.log('BackgroundImageUpload: Local file processed successfully');
      message.success(t('theme:background.uploadSuccess', '背景图片上传成功'));
    } catch (error) {
      console.error('BackgroundImageUpload: Local file processing failed:', error);
      
      // 直接显示详细的错误信息，ImageProcessor已经提供了用户友好的错误消息
      let errorMessage = t('theme:background.uploadFailed', '背景图片上传失败');
      if (error instanceof Error) {
        // 使用ImageProcessor提供的详细错误消息
        errorMessage = error.message;
      }
      
      message.error(errorMessage);
      onError?.(error as Error);
    } finally {
      setProcessing(false);
      setProcessProgress(0);
    }
  }, [onImageSelect, t]);

  // 处理URL图片
  const handleUrlSubmit = useCallback(async () => {
    if (!urlInput.trim()) {
      message.warning(t('theme:background.enterUrl', '请输入图片链接'));
      return;
    }

    const validation = ImageProcessor.validateImageUrl(urlInput);
    if (!validation.valid) {
      message.error(validation.error);
      return;
    }

    try {
      setUrlLoading(true);
      
      message.info(t('theme:background.downloadingImage', '正在下载网络图片...'));
      
      // 使用新的下载方法下载图片
      const file = await ImageProcessor.downloadImageFromUrl(urlInput, 30000); // 30秒超时
      
      // 验证下载的文件
      const validation = ImageProcessor.validateImageFile(file);
      if (!validation.valid) {
        message.error(validation.error);
        return;
      }

      // 获取图片元数据
      const metadata = await ImageProcessor.getImageMetadata(file);
      setImageMetadata(metadata);

      // 检查是否需要压缩
      const shouldCompress = ImageProcessor.shouldCompress(metadata);
      let processedImage: ProcessedImage;

      if (shouldCompress) {
        const compressionOptions = ImageProcessor.getRecommendedCompression(metadata);
        processedImage = await ImageProcessor.processImage(file, compressionOptions);
        
        message.info(
          `网络图片已压缩：${(metadata.size / 1024 / 1024).toFixed(2)}MB → ${(processedImage.metadata.size / 1024 / 1024).toFixed(2)}MB`
        );
      } else {
        processedImage = await ImageProcessor.processImage(file, { quality: 0.95 });
      }

      setPreviewImage(processedImage.dataUrl);
      onImageSelect(processedImage);

      message.success(t('theme:background.urlLoadSuccess', '网络图片加载成功'));
      setUrlInput('');
    } catch (error) {
      console.error('URL image load failed:', error);
      
      // 提供更详细的错误信息
      let errorMessage = t('theme:background.urlLoadFailed', '网络图片加载失败');
      if (error instanceof Error) {
        if (error.message.includes('超时')) {
          errorMessage = t('theme:background.imageNetwork.downloadTimeoutRetry', '网络图片下载超时，请检查网络连接或稍后重试');
        } else if (error.message.includes('CORS')) {
          errorMessage = t('theme:background.imageNetwork.corsAccessDenied', '无法访问此图片，服务器不允许跨域请求');
        } else if (error.message.includes('网络错误')) {
          errorMessage = t('theme:background.imageNetwork.networkConnectionError', '网络连接错误，请检查网络设置');
        } else if (error.message.includes('HTTP')) {
          errorMessage = t('theme:background.imageNetwork.serverError', '服务器错误: {{message}}', { message: error.message });
        } else if (error.message.includes('文件过大')) {
          errorMessage = error.message;
        } else if (error.message.includes('内容类型')) {
          errorMessage = t('theme:background.imageNetwork.invalidImageLink', '链接不是有效的图片文件');
        } else {
          errorMessage = t('theme:background.imageNetwork.genericError', '{{baseMessage}}: {{details}}', {
            baseMessage: errorMessage,
            details: error.message
          });
        }
      }
      
      message.error(errorMessage);
    } finally {
      setUrlLoading(false);
    }
  }, [urlInput, onImageSelect, t]);

  // 移除图片
  const handleRemoveImage = useCallback(() => {
    setFileList([]);
    setPreviewImage(null);
    setImageMetadata(null);
    onImageRemove();
    message.success(t('theme:background.removeSuccess', '背景图片已移除'));
  }, [onImageRemove, t]);

  // 文件变更处理
  const handleFileChange: UploadProps['onChange'] = useCallback((info: any) => {
    setFileList(info.fileList);
  }, []);

  // 渲染图片信息
  const renderImageInfo = () => {
    if (!imageMetadata) return null;

    return (
      <Card size="small" title={t('theme:background.imageInfo', '图片信息')}>
        <Row gutter={[16, 8]}>
          <Col span={12}>
            <Text type="secondary">{t('theme:background.dimensions', '尺寸')}:</Text>
            <br />
            <Text>{imageMetadata.width} × {imageMetadata.height}</Text>
          </Col>
          <Col span={12}>
            <Text type="secondary">{t('theme:background.size', '大小')}:</Text>
            <br />
            <Text>{(imageMetadata.size / 1024 / 1024).toFixed(2)} MB</Text>
          </Col>
          <Col span={12}>
            <Text type="secondary">{t('theme:background.format', '格式')}:</Text>
            <br />
            <Text>{imageMetadata.format.toUpperCase()}</Text>
          </Col>
          <Col span={12}>
            <Text type="secondary">{t('theme:background.aspectRatio', '宽高比')}:</Text>
            <br />
            <Text>{imageMetadata.aspectRatio.toFixed(2)}</Text>
          </Col>
        </Row>
      </Card>
    );
  };

  // 渲染预览
  const renderPreview = () => {
    const imageToShow = previewImage || currentImage;
    
    if (!imageToShow) return null;

    return (
      <Card 
        size="small" 
        title={t('theme:background.preview', '预览')}
        extra={
          <Space>
            <Tooltip title={t('theme:background.viewFullSize', '查看原图')}>
              <Button 
                type="text" 
                icon={<EyeOutlined />} 
                onClick={() => {
                  // 在新窗口中打开图片
                  window.open(imageToShow, '_blank');
                }}
              />
            </Tooltip>
            <Tooltip title={t('theme:background.removeImage', '移除图片')}>
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
                onClick={handleRemoveImage}
              />
            </Tooltip>
          </Space>
        }
      >
        <div style={{ textAlign: 'center' }}>
          <Image
            src={imageToShow}
            alt="Background Preview"
            style={{ 
              maxWidth: '100%', 
              maxHeight: '200px',
              borderRadius: '8px',
            }}
            preview={{
              mask: <EyeOutlined />,
            }}
          />
        </div>
      </Card>
    );
  };

  return (
    <div className="background-image-upload">
      <Tabs defaultActiveKey="local" size="small">
        <TabPane 
          tab={
            <span>
              <UploadOutlined />
              {t('theme:background.localImage', '本地图片')}
            </span>
          } 
          key="local"
        >
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <Dragger
              name="backgroundImage"
              multiple={false}
              fileList={fileList}
              customRequest={handleFileUpload}
              onChange={handleFileChange}
              accept="image/*"
              disabled={processing || loading}
              showUploadList={false}
            >
              <p className="ant-upload-drag-icon">
                {processing ? <Spin size="large" /> : <UploadOutlined />}
              </p>
              <p className="ant-upload-text">
                {processing 
                  ? t('theme:background.processing', '正在处理图片...')
                  : t('theme:background.dragOrClick', '点击或拖拽图片到此区域上传')
                }
              </p>
              <p className="ant-upload-hint">
                {t('theme:background.supportFormats', '支持 JPG、PNG、GIF、WebP 格式，最大 50MB')}
              </p>
            </Dragger>

            {processing && (
              <Progress 
                percent={processProgress} 
                status="active"
                format={(percent) => `${percent}% ${t('theme:background.processing', '处理中')}`}
              />
            )}

            <Alert
              message={t('theme:background.compressionTip', '压缩提示')}
              description={t('theme:background.compressionDesc', '大尺寸图片将自动压缩以优化性能，同时保持良好的视觉效果。')}
              type="info"
              icon={<CompressOutlined />}
              showIcon
            />
          </Space>
        </TabPane>

        <TabPane 
          tab={
            <span>
              <LinkOutlined />
              {t('theme:background.urlImage', '网络图片')}
            </span>
          } 
          key="url"
        >
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <Input.Search
              placeholder={t('theme:background.enterImageUrl', '请输入图片链接 (https://...)')}
              value={urlInput}
              onChange={(e) => setUrlInput(e.target.value)}
              onSearch={handleUrlSubmit}
              loading={urlLoading}
              disabled={loading}
              enterButton={t('common:load', '加载')}
            />

            <Alert
              message={t('theme:background.urlTip', '网络图片提示')}
              description={t('theme:background.urlDesc', '请确保图片链接可以正常访问，支持 HTTPS 协议的图片链接。')}
              type="info"
              icon={<InfoCircleOutlined />}
              showIcon
            />
          </Space>
        </TabPane>
      </Tabs>

      {/* 图片预览 */}
      {renderPreview()}

      {/* 图片信息 */}
      {renderImageInfo()}
    </div>
  );
};

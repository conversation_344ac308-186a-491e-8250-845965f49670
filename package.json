{"name": "quickstart", "version": "1.0.0", "description": "QuickStart - 强大的文件快速启动工具，采用TypeScript + React + Electron架构，集成Ant Design UI框架和Apple风格设计", "main": "build/main/main.js", "homepage": "./", "author": {"name": "QuickStart Team", "email": "<EMAIL>"}, "license": "MIT", "private": true, "scripts": {"start": "npm run build && chcp 65001 >nul 2>&1 && cross-env NODE_ENV=production electron .", "start:dev": "chcp 65001 >nul 2>&1 && cross-env NODE_ENV=development electron .", "dev": "concurrently \"npm run dev:renderer\" \"wait-on http://localhost:3000 && npm run build:main:dev && npm run start:dev\"", "dev:quick": "npm run build:main:dev && npm run start:dev", "dev:main": "npm run start:dev", "dev:renderer": "webpack serve --config webpack.renderer.config.js", "build:main:dev": "cross-env NODE_ENV=development webpack --config webpack.main.config.js", "build": "npm run build:renderer && npm run build:main && npm run build:preload", "build:main": "cross-env NODE_ENV=production webpack --config webpack.main.config.js", "build:preload": "cross-env NODE_ENV=production webpack --config webpack.preload.config.js", "build:renderer": "cross-env NODE_ENV=production webpack --config webpack.renderer.config.js", "pack": "electron-builder --dir", "dist": "electron-builder", "dist:win": "electron-builder --win", "dist:mac": "electron-builder --mac", "dist:linux": "electron-builder --linux", "clean": "<PERSON><PERSON><PERSON> dist build", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"@ant-design/v5-patch-for-react-19": "^1.0.3", "@types/uuid": "^10.0.0", "antd": "^5.26.3", "better-sqlite3": "^12.2.0", "chokidar": "^4.0.3", "colord": "^2.9.3", "dayjs": "^1.11.13", "electron": "^37.2.0", "electron-log": "^5.4.1", "electron-store": "^10.1.0", "electron-updater": "^6.6.2", "i18next": "^25.3.0", "i18next-browser-languagedetector": "^8.2.0", "neo-async": "^2.6.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.3", "uuid": "^11.1.0"}, "devDependencies": {"@electron/rebuild": "^4.0.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/better-sqlite3": "^7.6.13", "@types/jest": "^30.0.0", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "buffer": "^6.0.3", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "electron-builder": "^26.0.12", "eslint": "^9.30.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "jest-transform-stub": "^2.0.0", "less": "^4.3.0", "less-loader": "^12.3.0", "path-browserify": "^1.0.1", "prettier": "^3.6.2", "process": "^0.11.10", "rimraf": "^6.0.1", "stream-browserify": "^3.0.0", "style-loader": "^4.0.0", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "typescript": "^5.8.3", "url-loader": "^4.1.1", "util": "^0.12.5", "wait-on": "^8.0.3", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}, "build": {"appId": "com.quickstart.app", "productName": "QuickStart", "directories": {"output": "dist", "buildResources": "assets"}, "files": ["build/**/*", "assets/**/*", "node_modules/**/*"], "extraResources": [{"from": "assets/", "to": "assets/", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.productivity"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Utility"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}
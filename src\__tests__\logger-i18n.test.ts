/**
 * 日志系统国际化测试
 * 
 * 验证日志系统的国际化功能是否正常工作
 */

import { LogManager, LogLevel, LogSource, LogCategory } from '../shared/logger';

describe('Logger Internationalization', () => {
  let logManager: LogManager;

  beforeEach(() => {
    // 创建测试用的日志管理器
    logManager = new LogManager(LogSource.MAIN, {
      enableFile: false,
      enableConsole: true, // 启用控制台输出以便测试
      logDir: '/tmp/test-logs', // 提供一个有效的日志目录
    });
  });

  afterEach(() => {
    // 清理
    if (logManager) {
      logManager.removeAllListeners();
    }
  });

  test('should use fallback message when i18n function is not set', async () => {
    // 在Windows平台上，LogManager使用process.stdout.write，在其他平台使用console.info
    const isWindows = process.platform === 'win32';
    let outputSpy: jest.SpyInstance;

    if (isWindows) {
      outputSpy = jest.spyOn(process.stdout, 'write').mockImplementation(() => true);
    } else {
      outputSpy = jest.spyOn(console, 'info').mockImplementation();
    }

    await logManager.logI18n(
      LogLevel.INFO,
      'logs.app.startup.success',
      'Application started successfully, version: 1.0.0',
      LogCategory.APP,
      'test.ts',
      { version: '1.0.0' }
    );

    // 验证使用了回退消息
    expect(outputSpy).toHaveBeenCalled();
    outputSpy.mockRestore();
  });

  test('should use translated message when i18n function is set', async () => {
    const mockI18nFunction = jest.fn((key: string, fallback: string, params?: Record<string, unknown>) => {
      if (key === 'logs.app.startup.success') {
        return `应用程序启动成功，版本: ${params?.version}`;
      }
      return fallback;
    });

    logManager.setI18nFunction(mockI18nFunction);

    // 在Windows平台上，LogManager使用process.stdout.write，在其他平台使用console.info
    const isWindows = process.platform === 'win32';
    let outputSpy: jest.SpyInstance;

    if (isWindows) {
      outputSpy = jest.spyOn(process.stdout, 'write').mockImplementation(() => true);
    } else {
      outputSpy = jest.spyOn(console, 'info').mockImplementation();
    }

    await logManager.logI18n(
      LogLevel.INFO,
      'logs.app.startup.success',
      'Application started successfully, version: 1.0.0',
      LogCategory.APP,
      'test.ts',
      { version: '1.0.0' }
    );

    // 验证调用了翻译函数
    expect(mockI18nFunction).toHaveBeenCalledWith(
      'logs.app.startup.success',
      'Application started successfully, version: 1.0.0',
      { version: '1.0.0' }
    );

    outputSpy.mockRestore();
  });

  test('should handle translation errors gracefully', async () => {
    const mockI18nFunction = jest.fn(() => {
      throw new Error('Translation failed');
    });

    logManager.setI18nFunction(mockI18nFunction);

    const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();

    // 在Windows平台上，LogManager使用process.stdout.write，在其他平台使用console.info
    const isWindows = process.platform === 'win32';
    let outputSpy: jest.SpyInstance;

    if (isWindows) {
      outputSpy = jest.spyOn(process.stdout, 'write').mockImplementation(() => true);
    } else {
      outputSpy = jest.spyOn(console, 'info').mockImplementation();
    }

    await logManager.logI18n(
      LogLevel.INFO,
      'logs.app.startup.success',
      'Application started successfully',
      LogCategory.APP,
      'test.ts'
    );

    // 验证记录了翻译错误警告
    expect(consoleWarnSpy).toHaveBeenCalled();

    consoleWarnSpy.mockRestore();
    outputSpy.mockRestore();
  });

  test('should support language switching', () => {
    logManager.setLanguage('en');
    expect(logManager['currentLanguage']).toBe('en');

    logManager.setLanguage('zh-CN');
    expect(logManager['currentLanguage']).toBe('zh-CN');
  });
});

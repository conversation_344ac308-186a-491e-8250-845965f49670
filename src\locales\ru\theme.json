{"title": "Настройки темы", "presets": {"iosBlue": "iOS синий", "macosGreen": "macOS зеленый", "appleOrange": "Apple оранжевый", "deepSpaceGray": "Глубокий космос", "midnightBlue": "Полуночный синий"}, "colors": {"title": "Цвета интерфейса", "primary": "Основной цвет", "secondary": "Вторичный цвет", "background": "Цвет фона", "surface": "Цвет поверхности", "text": "Цвет текста", "textSecondary": "Цвет вторичного текста", "border": "Цвет границы", "success": "Цвет успеха", "warning": "Цвет предупреждения", "error": "Цвет ошибки", "info": "Цвет информации", "accent": "Акцентный цвет", "muted": "Приглушенный цвет", "highlight": "Цвет выделения", "shadow": "Цвет тени", "interface": "Цвета интерфейса", "labels": {"surface": "Цвет поверхности", "text": "Цвет текста", "textSecondary": "Вторичный текст", "border": "Цвет границы", "shadow": "Цвет тени"}, "descriptions": {"surface": "Цвет фона для карточек и панелей", "text": "Основной цвет текста", "textSecondary": "Цвет вторичного текста и описаний", "border": "Цвет границ и разделителей", "shadow": "Цвет теней и отбрасываемых теней"}}, "fonts": {"title": "Настройки шрифта", "family": "Семейство шрифтов", "size": "Размер шрифта", "weight": "Тол<PERSON>ина шрифта", "lineHeight": "Высота строки", "letterSpacing": "Межбуквенный интервал", "systemFont": "Системный шрифт", "customFont": "Пользовательский шрифт", "configuration": "Конфигурация шрифта", "options": {"appleSystem": "Системный шрифт Apple", "microsoftYaHei": "Microsoft YaHei", "pingFangSC": "PingFang SC", "helveticaNeue": "Helvetica Neue", "sfProDisplay": "SF Pro Display", "segoeUI": "Segoe UI"}, "weights": {"thin": "Тонкий", "light": "Светлый", "regular": "Обычный", "medium": "Средний", "semibold": "Полужирный", "bold": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heavy": "Тяжелый"}}, "effects": {"title": "Визуальные эффекты", "glassEffect": "Эффект матового стекла", "transparency": "Прозрачность", "blur": "Размытие", "shadows": "Эффекты теней", "animations": "Эффекты анимации", "transitions": "Эффекты переходов", "borderRadius": "Радиус границы", "gradient": "Эффекты градиента", "compactMode": "Компактный режим"}, "background": {"title": "Настройки фона", "type": "Тип фона", "color": "Сплошной цвет фона", "gradient": "Градиентный фон", "image": "Изображение фона", "pattern": "Узор фона", "opacity": "Прозрачность фона", "position": "Позиция фона", "size": "Размер фона", "repeat": "Повтор фона", "attachment": "Прикрепление фона", "blend": "Режим смешивания", "enabled": "Включить фон", "basicSettings": "Основные настройки", "colorSettings": "Настройки цвета", "gradientSettings": "Настройки градиента", "imageSettings": "Настройки изображения", "reset": "Сбросить настройки", "gradientType": "Тип градиента", "gradientDirection": "Направление градиента", "gradientColors": "Цвета градиента", "startColor": "Начальный", "endColor": "Конечный", "colorStop": "Цвет", "addColor": "Добавить цвет", "removeColor": "Удалить цвет", "noGradientColors": "Нет цветов градиента", "clickAddColor": "Нажмите кнопку \"Добавить цвет\" для добавления цветов градиента", "gradientTypes": {"linear": "Линейный градиент", "radial": "Радиальный градиент"}, "types": {"none": "Без фона", "color": "Сплошной цвет", "gradient": "Град<PERSON><PERSON><PERSON><PERSON>", "image": "Изображение"}, "performanceSettings": "Настройки производительности", "performanceTip": "Совет по производительности", "performanceDesc": "Фоновые изображения высокого разрешения могут влиять на производительность приложения. Рекомендуется использовать соответствующие настройки сжатия.", "localImage": "Локальное изображение", "networkImage": "Сетевое изображение", "dragOrClick": "Нажмите или перетащите изображение в эту область для загрузки", "supportFormats": "Поддерживает форматы JPG, PNG, GIF, WebP, максимум 50МБ", "compressionTip": "Совет по сжатию", "compressionDesc": "Большие изображения будут автоматически сжаты для оптимизации производительности при сохранении хорошего визуального эффекта.", "displayMode": "Режим отображения", "blur": "Размытие", "brightness": "Яркость", "contrast": "Контрастность", "saturation": "Насыщенность", "modes": {"stretch": "Растянуть", "tile": "Мозаика", "center": "По центру", "cover": "Покрыть", "contain": "Вместить"}, "processing": "Обработка изображения...", "urlLoadSuccess": "Сетевое изображение успешно загружено", "urlLoadFailed": "Не удалось загрузить сетевое изображение", "removeSuccess": "Фоновое изображение удалено", "preview": "Предварительный просмотр", "viewFullSize": "Просмотр в полном размере", "removeImage": "Удалить изображение", "loadError": "Не удалось загрузить конфигурацию фона", "noConfig": "Конфигурация фона недоступна", "configWillBeCreated": "Конфигурация будет создана при первом использовании", "urlImage": "Сетевое изображение", "enterImageUrl": "Введите URL изображения (https://...)", "urlTip": "Совет по сетевым изображениям", "urlDesc": "Убедитесь, что ссылка на изображение доступна и поддерживает протокол HTTPS.", "enterUrl": "Пожалуйста, введите ссылку на изображение", "uploadSuccess": "Фоновое изображение успешно загружено", "uploadFailed": "Не удалось загрузить фоновое изображение", "downloadingImage": "Загрузка сетевого изображения...", "processingLocalImage": "Обработка локального изображения...", "compressingImage": "Сжатие изображения для оптимизации производительности...", "savingImage": "Сохранение фонового изображения...", "imageSetSuccess": "Фоновое изображение успешно установлено", "imageSetFailed": "Не удалось установить фоновое изображение", "usingMemoryMode": "Использование режима памяти для установки фона (требует сброса после перезапуска)", "imageSetSuccessMemory": "Фоновое изображение успешно установлено (временный режим)", "imageSetFailedComplete": "Установка фонового изображения полностью не удалась", "defaultImageName": "Фоновое изображение", "duplicateFileName": "Дублирующееся имя файла", "duplicateFileContent": "Имя файла \"{{fileName}}\" уже существует, переименовать и сохранить?", "renameAndSave": "Переименовать и сохранить", "cancelSave": "Отменить сохранение фонового изображения", "permissionDeniedError": "Недостаточно прав доступа к каталогу кэша, проверьте разрешения приложения", "diskSpaceError": "Недостаточно места на диске, невозможно сохранить фоновое изображение", "cacheNotInitializedError": "Система кэша не инициализирована, повторная попытка...", "imageCacheFailedError": "Ошибка кэша изображения: {{message}}", "imageValidation": {"unsupportedFormat": "Неподдерживаемый формат изображения: {{format}}. Поддерживаемые форматы: JPEG, PNG, GIF, WebP", "fileTooLarge": "Файл изображения слишком большой: {{size}}МБ. Максимально поддерживается: 50МБ", "fileInvalid": "Недопустимый объект файла", "emptyFile": "Размер файла равен 0, возможно, это пустой файл", "invalidFileType": "Недопустимый тип файла: {{type}}, ожидается тип изображения", "loadTimeout": "Тайм-аут загрузки изображения (5 секунд), файл может быть поврежден или слишком большой", "readTimeout": "Тайм-аут чтения файла (10 секунд), файл может быть слишком большим или поврежденным", "invalidDimensions": "Недопустимые размеры изображения, файл может быть поврежден или не является допустимым форматом изображения", "cannotReadFile": "Невозможно прочитать файл изображения", "jpegCorrupted": "JPEG файл существует, но может быть поврежден, попробуйте пересохранить с помощью редактора изображений", "pngCorrupted": "PNG файл существует, но может быть поврежден, попробуйте пересохранить с помощью редактора изображений", "gifCorrupted": "GIF файл существует, но может быть поврежден, попробуйте пересохранить с помощью редактора изображений", "webpCorrupted": "WebP файл существует, но может быть поврежден или не поддерживается в текущей среде", "bmpCorrupted": "BMP файл существует, но может быть поврежден, рекомендуется конвертировать в JPG или PNG формат", "webpNotSupported": "WebP формат не поддерживается в текущем браузере, используйте JPG или PNG формат", "fileCorruptedGeneric": "Файл не является допустимым форматом изображения или поврежден. Заголовок файла: {{header}}...", "unsupportedFileType": "Тип файла не поддерживается: {{type}}. Выберите допустимый файл изображения (JPG, PNG, GIF, WebP)", "fileTooLargeSimple": "Файл изображения слишком большой, выберите изображение меньше 50МБ", "corruptionSuggestions": "Возможные причины: файл может быть поврежден; формат изображения может не поддерживаться; могут быть проблемы с правами доступа к файлу"}, "imageProcessing": {"setImageSourceFailed": "Не удалось установить источник изображения: {{message}}", "fileReadFailed": "Не удалось прочитать файл: {{message}}", "unknownError": "Неизвестная ошибка"}, "imageNetwork": {"downloadTimeoutRetry": "Тайм-аут загрузки сетевого изображения, проверьте сетевое подключение или повторите попытку позже", "corsAccessDenied": "Невозможно получить доступ к этому изображению, сервер не разрешает кросс-доменные запросы", "networkConnectionError": "Ошибка сетевого подключения, проверьте настройки сети", "serverError": "Ошибка сервера: {{message}}", "fileTooLargeNetwork": "Файл сетевого изображения слишком большой", "invalidImageLink": "Ссылка не является действительным файлом изображения", "genericError": "{{baseMessage}}: {{details}}"}, "cache": {"initializationFailed": "Инициализация менеджера кэша не удалась: {{message}}", "permissionDeniedCreate": "Доступ запрещен: Невозможно создать или записать в каталог кэша: {{dir}}. Проверьте права доступа к папке.", "insufficientDiskSpaceCreate": "Недостаточно места на диске для создания каталога кэша: {{dir}}", "invalidPathCreate": "Недопустимый путь: Файл существует там, где должен быть создан каталог: {{dir}}", "ensureCacheDirectoryFailed": "Не удалось обеспечить каталог кэша: {{message}}", "insufficientDiskSpaceCache": "Недостаточно места на диске: требуется {{required}}МБ, доступно {{available}}МБ", "fileWriteVerificationFailed": "Проверка записи файла не удалась: ожидалось {{expected}} байт, получено {{actual}} байт", "insufficientDiskSpace": "Недостаточно места на диске для кэширования изображения", "permissionDeniedWrite": "Доступ запрещен: Невозможно записать в каталог кэша: {{dir}}", "tooManyOpenFiles": "Слишком много открытых файлов: Невозможно кэшировать изображение", "cacheImageFailed": "Не удалось кэшировать изображение: {{message}}", "cleanupPartialFileFailed": "Не удалось очистить частичный файл"}, "cachedImages": "Кэшированные изображения", "noCachedImages": "Нет кэшированных изображений", "cachedImagesDesc": "Нажмите на изображение для переключения фона, нажмите кнопку удаления для удаления из кэша", "switchToImage": "Переключиться на этот фон", "removeFromCache": "Удалить из кэша", "confirmRemoveCache": "Подтвердить удаление кэша", "confirmRemoveCacheContent": "Вы уверены, что хотите удалить изображение \"{{fileName}}\" из кэша? Это действие нельзя отменить.", "removeCacheSuccess": "Изображение удалено из кэша", "removeCacheFailed": "Не удалось удалить кэшированное изображение", "imageCache": "Кэш изображений", "cacheManagement": "Управление кэшем", "customFileName": "Пользовательское имя файла", "enterCustomFileName": "Пожалуйста, введите пользовательское имя файла", "fileNameInvalid": "Недопустимое имя файла", "renameImage": "Переименовать изображение", "newFileName": "Новое имя файла", "renameSuccess": "Переименование успешно", "renameFailed": "Переименование не удалось", "enterNewFileName": "Пожалуйста, введите новое имя файла"}, "actions": {"apply": "Применить тему", "reset": "Сбросить тему", "customize": "Настроить тему", "duplicate": "Дублировать тему", "rename": "Переименовать тему", "share": "Поделиться темой"}, "messages": {"themeApplied": "Тема применена", "themeReset": "Тема сброшена", "themeSaved": "Тема сохранена", "themeLoaded": "Тема загружена", "themeExported": "Конфигурация темы экспортирована", "themeImported": "Конфигурация темы импортирована", "themeImportError": "Ошибка формата файла конфигурации темы", "layoutUpdated": "Настройки макета обновлены", "layoutUpdateFailed": "Не удалось обновить настройки макета", "layoutUpdateError": "Ошибка при обновлении настроек макета", "loadingLayout": "Загрузка настроек макета...", "loadLayoutFailed": "Не удалось загрузить настройки макета", "noLayoutConfig": "Невозможно загрузить настройки макета", "checkConfigSystem": "Пожалуйста, проверьте, работает ли система конфигурации правильно", "invalidThemeFile": "Неверный файл темы", "themeNameRequired": "Пожалуйста, введите имя темы", "themeNameExists": "Имя темы уже существует", "confirmDeleteTheme": "Подтвердить удаление этой темы?", "confirmResetTheme": "Подтвердить сброс настроек темы?", "themeDeleteConfirm": "Подтвердить удаление этой темы?", "themeResetConfirm": "Подтвердить сброс темы к значениям по умолчанию?", "customThemeNameRequired": "Требуется имя пользовательской темы", "customThemeNameExists": "Имя пользовательской темы уже существует", "themeFileInvalid": "Неверный формат файла темы", "themeLoadError": "Ошибка загрузки темы", "themeSaveError": "Ошибка сохранения темы", "previewMode": "Режим предварительного просмотра включен", "previewModeDisabled": "Режим предварительного просмотра отключен", "colorPickerTitle": "Выбрать цвет", "resetToDefault": "Сбросить к значениям по умолчанию", "importTheme": "Импортировать тему", "exportTheme": "Экспортировать тему", "themePreview": "Предварительный просмотр темы", "applyChanges": "Применить изменения", "discardChanges": "Отменить изменения", "unsavedChanges": "У вас есть несохраненные изменения темы"}, "layout": {"title": "Настройки макета", "sidebar": "Боковая панель", "sidebarWidth": "<PERSON>и<PERSON><PERSON>на боковой панели", "sidebarPosition": "Позиция боковой панели"}, "global": {"title": "Глобальная тема", "primaryColor": "Основной цвет", "primaryColorDesc": "Влияет на цвет кнопок, ссылок и других основных элементов", "mode": "Режим отображения", "darkMode": "Темный режим", "modeDesc": "Переключение между светлым и темным режимами отображения", "font": "Настройки шрифта", "fontSize": "Размер шрифта", "borderRadius": "Радиус границы"}, "ui": {"title": "Настройки стиля", "description": "Настройка внешнего вида и темы приложения", "interfaceColors": "Цвета интерфейса", "themePresets": "Предустановки тем", "typography": "Типографика", "visualEffects": "Визуальные эффекты", "layoutSettings": "Настройки макета", "primaryColorDesc": "Используется для кнопок, ссылок и других основных элементов", "colorLabels": {"background": "Фон", "surface": "Поверхность", "text": "Текст", "textSecondary": "Вторичный текст", "border": "Гра<PERSON><PERSON><PERSON>а", "shadow": "Тень"}, "fontSize": "Размер шрифта", "interfaceEffects": "Эффекты интерфейса", "sidebar": {"title": "Настройки боковой панели", "position": "Положение боковой панели", "width": "<PERSON>и<PERSON><PERSON>на боковой панели", "collapsed": "Свернуто по умолчанию", "autoHide": "Автоскрытие", "positions": {"left": "Слева", "right": "Справа"}}, "fileList": {"title": "Настройки списка файлов", "showSize": "Показывать размер", "showModifiedTime": "Показывать время изменения", "showAddedTime": "Показывать время добавления", "showLaunchCount": "Показывать количество запусков"}, "statusBar": {"title": "Настройки строки состояния", "visible": "Показывать строку состояния", "showFileCount": "Показывать количество файлов", "showPath": "Показывать путь"}}}
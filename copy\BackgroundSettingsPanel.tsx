import React, { useState, useCallback } from 'react';
import {
  Card,
  Row,
  Col,
  Typography,
  Switch,
  Slider,
  Select,
  Space,
  Divider,
  ColorPicker,
  Button,
  Tooltip,
  Alert,
  Collapse,
} from 'antd';
import {
  BgColorsOutlined,
  PictureOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
  PlusOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { Color } from 'antd/es/color-picker';
import { useBackgroundConfig } from '../hooks/useBackgroundConfig';
import { BackgroundImageUpload } from './BackgroundImageUpload';
import type { ProcessedImage } from '../utils/imageProcessor';

const { Title, Text } = Typography;
const { Option } = Select;
const { Panel } = Collapse;

export const BackgroundSettingsPanel: React.FC = () => {
  const { t } = useTranslation(['theme', 'common']);
  const {
    config,
    loading,
    error,
    updateConfig,
    resetConfig,
    setBackground,
    cacheImage,
  } = useBackgroundConfig();

  const [activeKey, setActiveKey] = useState<string[]>(['basic']);

  // 颜色选择器打开状态管理
  const [colorPickerOpen, setColorPickerOpen] = useState<{
    background: boolean;
    [key: string]: boolean; // 支持动态的渐变颜色选择器
  }>({
    background: false,
  });

  // 处理背景类型变更
  const handleTypeChange = useCallback(async (type: 'none' | 'color' | 'gradient' | 'image' | 'url') => {
    if (type === 'gradient') {
      // 切换到渐变模式时，确保有默认的渐变配置
      await setBackground({
        type,
        gradient: {
          type: 'linear',
          direction: 45,
          colors: [
            { color: '#1890ff', position: 0 },
            { color: '#52c41a', position: 100 },
          ],
          opacity: 1.0,
        }
      });
    } else {
      await setBackground({ type });
    }
  }, [setBackground]);

  // 处理背景启用/禁用
  const handleEnabledChange = useCallback(async (enabled: boolean) => {
    await updateConfig({ enabled });
  }, [updateConfig]);

  // 处理颜色选择器打开/关闭状态
  const handleColorPickerOpenChange = useCallback((key: string, open: boolean) => {
    setColorPickerOpen(prev => ({
      ...prev,
      [key]: open,
    }));
  }, []);

  // 处理颜色变更
  const handleColorChange = useCallback(async (color: Color) => {
    if (!config) return;

    await updateConfig({
      color: {
        ...config.color,
        value: color.toHexString(),
      },
    });
  }, [config, updateConfig]);

  // 处理透明度变更
  const handleOpacityChange = useCallback(async (opacity: number) => {
    if (!config) return;
    
    const newOpacity = opacity / 100;
    
    switch (config.type) {
      case 'color':
        await updateConfig({
          color: { ...config.color, opacity: newOpacity },
        });
        break;
      case 'gradient':
        await updateConfig({
          gradient: { ...config.gradient, opacity: newOpacity },
        });
        break;
      case 'image':
        await updateConfig({
          image: { ...config.image, opacity: newOpacity },
        });
        break;
    }
  }, [config, updateConfig]);

  // 处理图片显示模式变更
  const handleDisplayModeChange = useCallback(async (displayMode: string) => {
    if (!config || config.type !== 'image') return;
    
    await updateConfig({
      image: {
        ...config.image,
        displayMode: displayMode as any,
      },
    });
  }, [config, updateConfig]);

  // 处理渐变类型变更
  const handleGradientTypeChange = useCallback(async (type: 'linear' | 'radial') => {
    if (!config || config.type !== 'gradient') return;

    await updateConfig({
      gradient: {
        ...config.gradient,
        type,
      },
    });
  }, [config, updateConfig]);

  // 处理渐变方向变更
  const handleGradientDirectionChange = useCallback(async (direction: number) => {
    if (!config || config.type !== 'gradient') return;

    await updateConfig({
      gradient: {
        ...config.gradient,
        direction,
      },
    });
  }, [config, updateConfig]);

  // 处理渐变颜色变更
  const handleGradientColorChange = useCallback(async (index: number, color: Color) => {
    if (!config || config.type !== 'gradient') return;

    // 确保渐变颜色数组存在且是数组
    const currentColors = Array.isArray(config.gradient.colors) ? config.gradient.colors : [];

    if (index >= currentColors.length) return;

    const newColors = [...currentColors];
    newColors[index] = {
      ...newColors[index],
      color: color.toHexString(),
    };

    await updateConfig({
      gradient: {
        ...config.gradient,
        colors: newColors,
      },
    });
  }, [config, updateConfig]);

  // 计算渐变颜色位置
  const calculateGradientPositions = useCallback((colorCount: number): number[] => {
    if (colorCount <= 1) return [0];
    if (colorCount === 2) return [0, 100];

    const positions: number[] = [];
    const step = 100 / (colorCount - 1);

    for (let i = 0; i < colorCount; i++) {
      positions.push(Math.round(i * step));
    }

    return positions;
  }, []);

  // 添加渐变颜色
  const handleAddGradientColor = useCallback(async () => {
    if (!config || config.type !== 'gradient') return;

    // 确保渐变颜色数组存在且是数组
    const currentColors = Array.isArray(config.gradient.colors) ? config.gradient.colors : [];

    if (currentColors.length >= 5) return;

    const newColorCount = currentColors.length + 1;
    const newPositions = calculateGradientPositions(newColorCount);

    // 创建新的颜色数组，保持现有颜色，添加新颜色
    const newColors = currentColors.map((color, index) => ({
      ...color,
      position: newPositions[index],
    }));

    // 添加新颜色（使用最后一个颜色作为默认值，如果没有则使用默认颜色）
    const lastColor = currentColors.length > 0 ? currentColors[currentColors.length - 1] : { color: '#1890ff' };
    newColors.push({
      color: lastColor.color,
      position: newPositions[newColorCount - 1],
    });

    await updateConfig({
      gradient: {
        ...config.gradient,
        colors: newColors,
      },
    });
  }, [config, updateConfig, calculateGradientPositions]);

  // 删除渐变颜色
  const handleRemoveGradientColor = useCallback(async (index: number) => {
    if (!config || config.type !== 'gradient') return;

    // 确保渐变颜色数组存在且是数组
    const currentColors = Array.isArray(config.gradient.colors) ? config.gradient.colors : [];

    if (currentColors.length <= 2) return;

    const newColors = currentColors.filter((_, i) => i !== index);
    const newPositions = calculateGradientPositions(newColors.length);

    // 重新计算位置
    const updatedColors = newColors.map((color, i) => ({
      ...color,
      position: newPositions[i],
    }));

    await updateConfig({
      gradient: {
        ...config.gradient,
        colors: updatedColors,
      },
    });
  }, [config, updateConfig, calculateGradientPositions]);

  // 处理图片效果变更
  const handleImageEffectChange = useCallback(async (effect: string, value: number) => {
    if (!config || config.type !== 'image') return;

    await updateConfig({
      image: {
        ...config.image,
        [effect]: value,
      },
    });
  }, [config, updateConfig]);

  // 处理图片选择
  const handleImageSelect = useCallback(async (image: ProcessedImage) => {
    try {
      // 将Blob转换为ArrayBuffer用于缓存
      const arrayBuffer = await image.blob.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);

      // 生成唯一的原始路径标识符
      const originalPath = `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 缓存图片 - 传递Uint8Array而不是Buffer
      const cachedPath = await cacheImage(originalPath, uint8Array, image.metadata);

      // 设置背景配置
      await setBackground({
        type: 'image',
        image: {
          source: 'local',
          path: cachedPath,
          displayMode: 'cover',
          opacity: 1.0,
          blur: 0,
          brightness: 100,
          contrast: 100,
          saturation: 100,
          position: { x: 50, y: 50 },
          scale: 1.0,
          rotation: 0,
        },
      });
    } catch (error) {
      console.error('Failed to cache and set background image:', error);
      // 如果缓存失败，仍然设置背景但使用dataUrl
      await setBackground({
        type: 'image',
        image: {
          source: 'local',
          path: image.dataUrl,
          displayMode: 'cover',
          opacity: 1.0,
          blur: 0,
          brightness: 100,
          contrast: 100,
          saturation: 100,
          position: { x: 50, y: 50 },
          scale: 1.0,
          rotation: 0,
        },
      });
    }
  }, [setBackground, cacheImage]);

  // 处理图片移除
  const handleImageRemove = useCallback(async () => {
    await setBackground({ type: 'none' });
  }, [setBackground]);

  // 重置配置
  const handleReset = useCallback(async () => {
    await resetConfig();
  }, [resetConfig]);

  if (loading) {
    return (
      <Card loading={true}>
        <div style={{ height: '200px' }} />
      </Card>
    );
  }

  if (error) {
    console.warn('Background config error:', error);
    return (
      <Card>
        <Alert
          message={t('theme:background.loadError', '加载背景配置失败')}
          description={error}
          type="warning"
          showIcon
          action={
            <Button size="small" onClick={() => window.location.reload()}>
              {t('common:retry', '重试')}
            </Button>
          }
        />
      </Card>
    );
  }

  if (!config) {
    console.warn('Background config not available');
    return (
      <Card>
        <Alert
          message={t('theme:background.noConfig', '背景配置不可用')}
          description={t('theme:background.configWillBeCreated', '配置将在首次使用时创建')}
          type="info"
          showIcon
        />
      </Card>
    );
  }

  // 调试信息
  console.log('Current background config:', {
    enabled: config.enabled,
    type: config.type,
    gradientColors: config.gradient?.colors?.length || 0,
    gradientColorsData: config.gradient?.colors
  });

  return (
    <div className="background-settings-panel">
      <Card
        title={
          <Space>
            <PictureOutlined />
            {t('theme:background.title', '背景设置')}
          </Space>
        }
        extra={
          <Tooltip title={t('theme:background.reset', '重置设置')}>
            <Button 
              type="text" 
              icon={<ReloadOutlined />} 
              onClick={handleReset}
            />
          </Tooltip>
        }
      >
        <Collapse 
          activeKey={activeKey} 
          onChange={setActiveKey}
          ghost
        >
          {/* 基础设置 */}
          <Panel 
            header={t('theme:background.basicSettings', '基础设置')} 
            key="basic"
            extra={<SettingOutlined />}
          >
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              {/* 启用背景 */}
              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('theme:background.enabled', '启用背景')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={config.enabled}
                    onChange={handleEnabledChange}
                  />
                </Col>
              </Row>

              {config.enabled && (
                <>
                  {/* 背景类型 */}
                  <Row gutter={16} align="middle">
                    <Col span={8}>
                      <Text>{t('theme:background.type', '背景类型')}:</Text>
                    </Col>
                    <Col span={16}>
                      <Select
                        value={config.type}
                        onChange={handleTypeChange}
                        style={{ width: '100%' }}
                      >
                        <Option value="none">{t('theme:background.types.none', '无背景')}</Option>
                        <Option value="color">{t('theme:background.types.color', '纯色')}</Option>
                        <Option value="gradient">{t('theme:background.types.gradient', '渐变')}</Option>
                        <Option value="image">{t('theme:background.types.image', '图片')}</Option>
                      </Select>
                    </Col>
                  </Row>

                  {/* 透明度 */}
                  {config.type !== 'none' && (
                    <Row gutter={16} align="middle">
                      <Col span={8}>
                        <Text>{t('theme:background.opacity', '透明度')}:</Text>
                      </Col>
                      <Col span={16}>
                        <Slider
                          min={0}
                          max={100}
                          value={
                            config.type === 'color' ? config.color.opacity * 100 :
                            config.type === 'gradient' ? config.gradient.opacity * 100 :
                            config.type === 'image' ? config.image.opacity * 100 : 100
                          }
                          onChange={handleOpacityChange}
                          tooltip={{ formatter: (value) => `${value}%` }}
                        />
                      </Col>
                    </Row>
                  )}
                </>
              )}
            </Space>
          </Panel>

          {/* 颜色设置 */}
          {config.enabled && config.type === 'color' && (
            <Panel
              header={t('theme:background.colorSettings', '颜色设置')}
              key="color"
              extra={<BgColorsOutlined />}
            >
              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('theme:background.color', '背景颜色')}:</Text>
                </Col>
                <Col span={16}>
                  <ColorPicker
                    value={config.color.value}
                    onChange={handleColorChange}
                    onOpenChange={(open) => handleColorPickerOpenChange('background', open)}
                    open={colorPickerOpen.background}
                    showText
                    trigger="click"
                  />
                </Col>
              </Row>
            </Panel>
          )}

          {/* 渐变设置 */}
          {config.enabled && config.type === 'gradient' && (
            <Panel
              header={t('theme:background.gradientSettings', '渐变设置')}
              key="gradient"
              extra={<BgColorsOutlined />}
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                {/* 渐变类型 */}
                <Row gutter={16} align="middle">
                  <Col span={8}>
                    <Text>{t('theme:background.gradientType', '渐变类型')}:</Text>
                  </Col>
                  <Col span={16}>
                    <Select
                      value={config.gradient.type}
                      onChange={(value) => handleGradientTypeChange(value as 'linear' | 'radial')}
                      style={{ width: '100%' }}
                    >
                      <Option value="linear">{t('theme:background.gradientTypes.linear', '线性渐变')}</Option>
                      <Option value="radial">{t('theme:background.gradientTypes.radial', '径向渐变')}</Option>
                    </Select>
                  </Col>
                </Row>

                {/* 渐变方向 (仅线性渐变) */}
                {config.gradient.type === 'linear' && (
                  <Row gutter={16} align="middle">
                    <Col span={8}>
                      <Text>{t('theme:background.gradientDirection', '渐变方向')}:</Text>
                    </Col>
                    <Col span={16}>
                      <Slider
                        min={0}
                        max={360}
                        value={config.gradient.direction}
                        onChange={handleGradientDirectionChange}
                        tooltip={{ formatter: (value) => `${value}°` }}
                      />
                    </Col>
                  </Row>
                )}

                {/* 渐变颜色 */}
                <div>
                  <Row justify="space-between" align="middle" style={{ marginBottom: '12px' }}>
                    <Col>
                      <Text>
                        {t('theme:background.gradientColors', '渐变颜色')}:
                      </Text>
                    </Col>
                    <Col>
                      <Button
                        type="text"
                        icon={<PlusOutlined />}
                        size="small"
                        onClick={handleAddGradientColor}
                        disabled={Array.isArray(config.gradient.colors) && config.gradient.colors.length >= 5}
                        title={t('theme:background.addColor', '添加颜色')}
                      >
                        {t('theme:background.addColor', '添加颜色')}
                      </Button>
                    </Col>
                  </Row>
                  <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    {Array.isArray(config.gradient.colors) && config.gradient.colors.length > 0 ? config.gradient.colors.map((colorStop, index) => (
                      <Row key={index} gutter={16} align="middle">
                        <Col span={6}>
                          <Text>
                            {index === 0
                              ? t('theme:background.startColor', '起始')
                              : (Array.isArray(config.gradient.colors) && index === config.gradient.colors.length - 1)
                                ? t('theme:background.endColor', '结束')
                                : `${t('theme:background.colorStop', '颜色')} ${index + 1}`
                            }:
                          </Text>
                        </Col>
                        <Col span={10}>
                          <ColorPicker
                            value={colorStop.color}
                            onChange={(color) => handleGradientColorChange(index, color)}
                            onOpenChange={(open) => handleColorPickerOpenChange(`gradient${index}`, open)}
                            open={colorPickerOpen[`gradient${index}`]}
                            showText
                            trigger="click"
                          />
                        </Col>
                        <Col span={4}>
                          <Text type="secondary">{colorStop.position}%</Text>
                        </Col>
                        <Col span={4}>
                          {Array.isArray(config.gradient.colors) && config.gradient.colors.length > 2 && (
                            <Button
                              type="text"
                              danger
                              icon={<DeleteOutlined />}
                              size="small"
                              onClick={() => handleRemoveGradientColor(index)}
                              title={t('theme:background.removeColor', '删除颜色')}
                            />
                          )}
                        </Col>
                      </Row>
                    )) : (
                      <Alert
                        message={t('theme:background.noGradientColors', '没有渐变颜色')}
                        description={t('theme:background.clickAddColor', '点击"添加颜色"按钮添加渐变颜色')}
                        type="info"
                        showIcon
                      />
                    )}
                  </Space>
                </div>
              </Space>
            </Panel>
          )}

          {/* 图片设置 */}
          {config.enabled && config.type === 'image' && (
            <Panel 
              header={t('theme:background.imageSettings', '图片设置')} 
              key="image"
              extra={<PictureOutlined />}
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                {/* 图片上传 */}
                <BackgroundImageUpload
                  onImageSelect={handleImageSelect}
                  onImageRemove={handleImageRemove}
                  currentImage={config.image.path}
                  loading={loading}
                />

                <Divider />

                {/* 显示模式 */}
                <Row gutter={16} align="middle">
                  <Col span={8}>
                    <Text>{t('theme:background.displayMode', '显示模式')}:</Text>
                  </Col>
                  <Col span={16}>
                    <Select
                      value={config.image.displayMode}
                      onChange={handleDisplayModeChange}
                      style={{ width: '100%' }}
                    >
                      <Option value="stretch">{t('theme:background.modes.stretch', '拉伸')}</Option>
                      <Option value="tile">{t('theme:background.modes.tile', '平铺')}</Option>
                      <Option value="center">{t('theme:background.modes.center', '居中')}</Option>
                      <Option value="cover">{t('theme:background.modes.cover', '覆盖')}</Option>
                      <Option value="contain">{t('theme:background.modes.contain', '包含')}</Option>
                    </Select>
                  </Col>
                </Row>

                {/* 图片效果 */}
                <Row gutter={16} align="middle">
                  <Col span={8}>
                    <Text>{t('theme:background.blur', '模糊度')}:</Text>
                  </Col>
                  <Col span={16}>
                    <Slider
                      min={0}
                      max={20}
                      value={config.image.blur}
                      onChange={(value) => handleImageEffectChange('blur', value)}
                      tooltip={{ formatter: (value) => `${value}px` }}
                    />
                  </Col>
                </Row>

                <Row gutter={16} align="middle">
                  <Col span={8}>
                    <Text>{t('theme:background.brightness', '亮度')}:</Text>
                  </Col>
                  <Col span={16}>
                    <Slider
                      min={0}
                      max={200}
                      value={config.image.brightness}
                      onChange={(value) => handleImageEffectChange('brightness', value)}
                      tooltip={{ formatter: (value) => `${value}%` }}
                    />
                  </Col>
                </Row>

                <Row gutter={16} align="middle">
                  <Col span={8}>
                    <Text>{t('theme:background.contrast', '对比度')}:</Text>
                  </Col>
                  <Col span={16}>
                    <Slider
                      min={0}
                      max={200}
                      value={config.image.contrast}
                      onChange={(value) => handleImageEffectChange('contrast', value)}
                      tooltip={{ formatter: (value) => `${value}%` }}
                    />
                  </Col>
                </Row>

                <Row gutter={16} align="middle">
                  <Col span={8}>
                    <Text>{t('theme:background.saturation', '饱和度')}:</Text>
                  </Col>
                  <Col span={16}>
                    <Slider
                      min={0}
                      max={200}
                      value={config.image.saturation}
                      onChange={(value) => handleImageEffectChange('saturation', value)}
                      tooltip={{ formatter: (value) => `${value}%` }}
                    />
                  </Col>
                </Row>
              </Space>
            </Panel>
          )}

          {/* 性能设置 */}
          <Panel 
            header={t('theme:background.performanceSettings', '性能设置')} 
            key="performance"
            extra={<InfoCircleOutlined />}
          >
            <Alert
              message={t('theme:background.performanceTip', '性能提示')}
              description={t('theme:background.performanceDesc', '高分辨率背景图片可能影响应用性能，建议使用适当的压缩设置。')}
              type="info"
              showIcon
            />
          </Panel>
        </Collapse>
      </Card>
    </div>
  );
};

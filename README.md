# QuickStart - 文件快速启动工具

## 项目概述

QuickStart 是一个强大的文件快速启动工具，采用 **TypeScript + React + Electron** 架构，集成 **Ant Design UI** 框架和 **Apple 风格设计**语言，为用户提供现代化的桌面应用体验。

## 🎯 项目进度

### ✅ 已完成的架构模块

#### 第一阶段：Electron进程通信架构 ✅
- ✅ **项目初始化** - 完整的 package.json 配置，TypeScript、ESLint、Prettier 设置
- ✅ **主进程架构** - 安全的 BrowserWindow 配置，启用 contextIsolation 和 sandbox 模式
- ✅ **预加载脚本** - 使用 contextBridge 安全暴露 API，实现分层 IPC 通信
- ✅ **IPC通信架构** - 统一的消息协议，支持文件操作、主题同步、配置管理等模块
- ✅ **基础渲染页面** - HTML 模板，CSP 安全策略，Apple 风格基础样式

### 🔄 开发中的模块

#### 第二阶段：核心样式系统架构 🔄
- ⏳ Ant Design 主题系统集成
- ⏳ Apple 风格设计语言实现
- ⏳ 动态主题切换机制
- ⏳ 毛玻璃效果和视觉特效

### 📋 待开发的模块

#### 第三阶段：配置管理系统 📋
- 📋 JSON 配置文件管理
- 📋 %APPDATA%\QuickStartAPP 目录结构
- 📋 配置持久化和版本控制
- 📋 配置备份和恢复机制

#### 第四阶段：国际化支持系统 📋
- 📋 i18next 国际化框架集成
- 📋 中文（简繁）、英语、俄语、法语支持
- 📋 实时语言切换
- 📋 语言资源文件管理

## 🛠️ 技术栈

### 前端技术栈
- **Electron** - 跨平台桌面应用框架
- **React** + **TypeScript** - 现代化前端开发
- **Ant Design** - 企业级 UI 组件库
- **Webpack** - 模块打包和构建工具

### 后端技术栈
- **Node.js** - Electron 主进程运行时
- **JSON 文件** - 轻量级配置存储

## 🚀 开发环境配置

### 环境要求
- **Node.js** >= 18.0.0
- **npm** >= 8.0.0

### 安装依赖
```bash
npm install
```

### 开发启动方式

#### 🔧 开发环境启动
```bash
# 完整开发环境（推荐）- 启动渲染进程开发服务器 + 主进程，支持热重载
npm run dev

# 快速开发启动 - 自动构建主进程然后启动
npm run dev:quick

# 标准开发启动 - 仅启动主进程（需要先手动构建）
npm run start:dev
```

#### 🏭 生产环境启动
```bash
# 生产环境启动 - 自动构建所有模块然后启动
npm start
```

#### 🔨 构建命令
```bash
# 构建所有模块
npm run build

# 单独构建主进程（开发版）
npm run build:main:dev

# 单独构建主进程（生产版）
npm run build:main

# 单独构建渲染进程
npm run build:renderer

# 单独构建预加载脚本
npm run build:preload
```

### 环境变量说明
- **开发环境**: `NODE_ENV=development` - 启用开发工具、热重载、详细日志
- **生产环境**: `NODE_ENV=production` - 优化性能、禁用调试功能
- **electron-log** - 日志管理
- **electron-store** - 数据持久化

### 开发工具
- **TypeScript** - 类型安全
- **ESLint** + **Prettier** - 代码规范
- **Jest** - 单元测试
- **electron-builder** - 应用打包

## 🏗️ 项目结构

```
QuickStart/
├── src/
│   ├── main/           # 主进程代码
│   │   ├── main.ts     # 主进程入口
│   │   └── ipc-handlers.ts  # IPC 处理器
│   ├── preload/        # 预加载脚本
│   │   └── preload.ts  # 安全 API 暴露
│   ├── renderer/       # 渲染进程代码
│   │   ├── index.html  # HTML 模板
│   │   └── index.tsx   # React 应用入口
│   └── shared/         # 共享代码
├── assets/             # 静态资源
├── docs/               # 项目文档
│   ├── TODO.md         # 开发计划
│   └── security.md     # 安全配置
├── build/              # 构建输出
├── dist/               # 打包输出
└── 配置文件...
```

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
# 构建所有模块
npm run build

# 启动开发服务器
npm run dev
```

### 生产构建
```bash
# 构建应用
npm run build

# 打包应用
npm run dist
```

## 🔒 安全特性

- ✅ **上下文隔离** - 启用 contextIsolation 防止代码注入
- ✅ **沙箱模式** - 限制渲染进程系统访问权限
- ✅ **CSP 策略** - 内容安全策略防止 XSS 攻击
- ✅ **API 白名单** - 严格限制暴露的 IPC 方法
- ✅ **路径验证** - 防止路径遍历攻击

## 📋 下一步计划

1. **完成核心样式系统** - 实现 Ant Design 主题集成和 Apple 风格设计
2. **建立配置管理** - 实现完整的配置文件管理系统
3. **集成国际化** - 添加多语言支持
4. **文件管理功能** - 实现文件添加、启动、图标获取等核心功能
5. **系统集成** - 托盘、通知、自启动等系统级功能

## 📄 许可证

MIT License

---

**注意**: 这是一个正在开发中的项目，当前已完成底层架构搭建，正在进行核心功能开发。

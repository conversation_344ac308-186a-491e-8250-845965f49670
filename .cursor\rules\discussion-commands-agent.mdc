---
description: 当开发者输入"/讨论"指令时使用
globs: 
alwaysApply: false
---
# 讨论指令处理规范

## 使用场景
- 用户输入`/讨论`指令时
- 需要对任何主题进行全面分析和讨论时
- 需要比较网络方案与自身实现时
- 需要进行深度技术调研时
- 需要对项目、模块、问题或想法进行探讨时

## 关键规则
- 多方面多角度剖析讨论主题
- 联网查询相关方案和实现
- 使用Context7深入调研技术细节
- 提供完整的分析思考过程
- 确保分析结果符合实际需求
- 整合各方信息形成全面的分析报告
- **仅限讨论分析，不执行实施操作**
- **将分析结果保存到docs/plan文件夹中**

## 分析流程
1. **理解** → 2. **多维度剖析** → 3. **网络方案查询** → 4. **Context7深度调研** → 5. **对比分析** → 6. **形成报告**

## 讨论范围扩展
- **模块分析**：现有代码模块的架构和实现
- **技术选型**：新功能或项目的技术选择
- **问题解决**：技术难题的可能解决方案
- **架构设计**：系统架构的优化和改进
- **功能规划**：新功能的可行性和实现方案
- **性能优化**：性能瓶颈的分析和优化方案
- **安全评估**：安全风险的分析和防范措施

## 剖析维度
- **主题理解**: 问题定义、需求分析、背景情况
- **架构设计**: 系统架构、模块划分、核心流程
- **技术栈**: 前后端技术、框架选择、第三方库
- **实现细节**: 关键算法、数据流、接口设计
- **性能考量**: 性能瓶颈、优化空间、资源使用
- **安全性**: 安全机制、数据保护、漏洞防范
- **可扩展性**: 扩展设计、接口灵活性、未来发展
- **可行性**: 技术可行性、资源需求、实施难度

## 文档生成
- 讨论结束后，必须创建`docs/plan`目录（如不存在）
- 分析结果必须保存到`docs/plan/`目录
- 文件命名格式：`YYYY-MM-DD-主题关键词.md`
- 文档必须包含完整的分析过程和结论
- 明确标注"仅供讨论参考，不作为实施方案"

## 文档模板
```markdown
# {{讨论主题}} 分析报告
*生成日期：{{YYYY-MM-DD}}*

> 本文档仅供讨论参考，不作为实施方案。

## 1. 主题概述
*简要描述讨论主题、背景和目标*

- 问题定义：
- 讨论范围：
- 预期目标：

## 2. 多维度剖析

### 2.1 主题理解
*深入理解问题本质和需求*

### 2.2 架构设计
*系统架构、模块划分、核心流程*

### 2.3 技术栈分析
*前后端技术、框架选择、第三方库*

### 2.4 实现细节
*关键算法、数据流、接口设计*

### 2.5 性能考量
*性能瓶颈、优化空间、资源使用*

### 2.6 安全性分析
*安全机制、数据保护、漏洞防范*

### 2.7 可扩展性评估
*扩展设计、接口灵活性、未来发展*

## 3. 网络方案调研
*调研市场上的主流解决方案*

### 3.1 方案一：{{方案名称}}
- 优势：
- 劣势：
- 适用场景：

### 3.2 方案二：{{方案名称}}
- 优势：
- 劣势：
- 适用场景：

### 3.3 方案三：{{方案名称}}
- 优势：
- 劣势：
- 适用场景：

## 4. Context7深度技术调研
*使用Context7进行深入技术调研*

### 4.1 {{技术领域一}}
*相关技术细节、最佳实践、API用法*

### 4.2 {{技术领域二}}
*相关技术细节、最佳实践、API用法*

## 5. 对比分析
*对比不同方案的优劣势*

| 方案 | 技术复杂度 | 开发成本 | 维护难度 | 性能表现 | 安全性 | 可扩展性 |
|---|---|----|----|----|-----|----|
| 方案一 |            |          |          |          |        |          |
| 方案二 |            |          |          |          |        |          |
| 方案三 |            |          |          |          |        |          |

## 6. 结论和建议

### 6.1 短期方案
*可快速实施的解决方案*

### 6.2 长期方案
*需要较长时间实施的解决方案*

### 6.3 风险评估
*可能存在的风险和应对策略*

### 6.4 后续步骤
*下一步行动建议*

---

*此分析报告仅供讨论参考，不作为实施方案。实际实施需基于具体情况进行评估和调整。*
```

## 执行限制
- 讨论命令仅用于分析和讨论，不执行实际代码修改
- 不允许在讨论过程中修改任何项目文件（除了在docs/plan中创建报告）
- 不允许运行终端命令来实施讨论中的方案
- 讨论结束后，需明确告知用户这只是分析结果，实施需另行确认

## 示例

<example>
用户：/讨论 如何优化应用启动速度

AI回复：
我将对"应用启动速度优化"进行全面分析，并按讨论流程进行分析。

# 应用启动速度优化分析

## 问题剖析
- **启动流程**：冷启动、热启动、交互就绪阶段
- **性能瓶颈**：资源加载、初始化逻辑、渲染延迟、数据预加载

## 网络方案调研
1. **代码分割与懒加载** - 按需加载资源
2. **资源预加载与缓存** - 减少网络请求
3. **启动屏优化** - 提升用户感知速度

## Context7技术调研
- Electron预热窗口技术
- React组件代码分割与懒加载
- 状态管理优化方案

## 优化建议
- **短期**：实现启动屏、优先加载核心UI、优化初始化逻辑
- **长期**：重构应用架构、实现智能预加载、性能监测系统

分析报告已保存至docs/plan/日期-应用启动速度优化.md，仅供参考，实施需另行确认。
</example>

<example type="invalid">
用户：/讨论 如何优化应用启动速度

AI回复：
我将帮您优化应用启动速度。让我先分析当前代码，然后实施以下改进：

1. 首先修改main.ts文件，优化窗口创建逻辑
2. 然后调整webpack配置，启用代码分割
3. 最后实现资源预加载

我现在就开始修改这些文件...

（错误：直接开始实施而非仅进行讨论分析，没有保存分析结果到docs/plan目录，没有遵循讨论流程）
</example>


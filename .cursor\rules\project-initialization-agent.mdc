---
description: 在新项目或缺少docs文件夹时自动初始化项目结构，创建标准模块化文档，确认项目信息并建立文档索引
globs: 
alwaysApply: false
---
# 项目初始化处理规范

## 使用场景
- 新项目开始时
- 缺少docs文件夹或模块文档时
- 项目结构不完整时

## 关键规则
- 检查并创建标准的docs/文件夹结构
- 根据项目类型创建对应的模块文档
- 建立文档索引和导航结构
- 确认项目基本信息并记录到README
- 设置文档模板和错误追踪格式

## 初始化流程
1. **项目分析** → 2. **结构检查** → 3. **文档创建** → 4. **索引建立** → 5. **模板设置**

## 标准文档结构（模块化）
docs/
├── README.md # 项目总文档和导航
├── TODO.md # 项目任务管理
├── error-tracking.md # 项目错误追踪
├── logging/ # 日志系统模块
│   ├── README.md # 日志系统主文档
│   └── i18n.md # 日志国际化专项
├── i18n/ # 国际化模块
│   ├── README.md # 国际化主文档
│   ├── completion-report.md # 完成度报告
│   └── CHANGELOG.md # 变更日志
├── database/ # 数据库模块
│   └── README.md # 数据库文档
├── api/ # API模块
│   └── README.md # API文档
├── backend/ # 后端模块
│   └── README.md # 后端文档
├── frontend/ # 前端模块
│   ├── README.md # 前端主文档
│   └── ui-improvements.md # UI改进实施
├── deployment/ # 部署模块
│   └── README.md # 部署文档
└── file-management/ # 文件管理模块
    └── implementation.md # 实现文档


## 示例

<example>
```text
🚀 初始化项目文档结构...

📁 创建docs模块化文件夹结构
📄 生成模块文档：
- ✅ docs/logging/ (日志系统模块)
- ✅ docs/i18n/ (国际化模块)
- ✅ docs/database/ (数据库模块)
- ✅ docs/api/ (API接口模块)
- ✅ docs/backend/ (后端模块)
- ✅ docs/frontend/ (前端模块)
- ✅ docs/deployment/ (部署模块)
- ✅ docs/file-management/ (文件管理模块)

📋 创建项目总文档 docs/README.md (包含完整导航)
📝 设置项目任务管理 docs/TODO.md
🔍 建立错误追踪 docs/error-tracking.md

项目文档结构初始化完成！
```
</example>

<example type="invalid">
只创建空的docs文件夹而不生成具体的模块文档

</example>
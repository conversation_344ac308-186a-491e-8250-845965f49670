---
description: 当开发者输入"/继续"指令时使用
globs: 
alwaysApply: false
---
# 继续指令处理规范

## 使用场景
- 用户输入`/继续`指令时
- 需要恢复中断的任务时
- 接续长输出内容时

## 关键规则
- 智能判断当前上下文和中断点
- 恢复之前的任务状态和进度
- 继续未完成的操作或输出
- 保持任务的连贯性和完整性
- 如无明确任务则分析项目状态

## 继续策略
- **任务恢复**: 检查最近的操作记录，继续未完成任务
- **输出接续**: 继续被截断的长内容输出
- **状态分析**: 无明确任务时分析项目状态

## 示例

<example>
```text
用户：/继续

AI回复：
🔄 检测到中断的任务...

📋 恢复上次操作：
- 上次任务：修改用户认证模块
- 中断点：正在更新 docs/auth.md 文档
- 进度：已完成代码修改，待完成文档更新

继续执行：
正在更新认证模块文档...
- ✅ 添加新的API接口说明
- ✅ 更新功能状态标识
- ✅ 记录修改历史

任务恢复完成！认证模块修改已全部完成。
```
</example>

<example type="invalid">
```text
用户：/继续

AI回复：
"继续什么？"
（错误：没有智能分析上下文，没有主动判断）
```

</example>
{"app": {"startup": {"success": "Приложение успешно запущено, версия: {{version}}", "failed": "Ошибка запуска приложения: {{error}}", "ready": "Приложение готово", "initializing": "Инициализация приложения...", "shutdown": "Приложение завершает работу", "crashed": "Приложение аварийно завершилось: {{error}}"}, "window": {"created": "Окно успешно создано: {{windowType}}", "failed": "Ошибка создания окна: {{error}}", "closed": "Окно закрыто: {{windowType}}", "minimized": "Окно свернуто", "maximized": "Окно развернуто", "restored": "Окно восстановлено"}, "tray": {"created": "Системный трей успешно создан", "failed": "Ошибка создания системного трея: {{error}}", "clicked": "Клик по системному трею"}}, "config": {"load": {"success": "Конфигурация успешно загружена: {{configType}}", "failed": "Ошибка загрузки конфигурации: {{configType}}, ошибка: {{error}}", "notFound": "Файл конфигурации не найден: {{configType}}, используются настройки по умолчанию", "corrupted": "Файл конфигурации поврежден: {{configType}}, сброшен к настройкам по умолчанию"}, "save": {"success": "Конфигурация успешно сохранена: {{configType}}", "failed": "Ошибка сохранения конфигурации: {{configType}}, ошибка: {{error}}"}, "backup": {"created": "Резервная копия конфигурации успешно создана: {{filename}}", "failed": "Ошибка создания резервной копии конфигурации: {{error}}", "restored": "Конфигурация успешно восстановлена из резервной копии: {{filename}}", "cleanup": "Очищено {{count}} устаревших файлов резервных копий"}, "reset": {"success": "Конфигурация успешно сброшена: {{configType}}", "failed": "Ошибка сброса конфигурации: {{configType}}, ошибка: {{error}}"}}, "db": {"connection": {"success": "Успешное подключение к базе данных: {{database}}", "failed": "Ошибка подключения к базе данных: {{database}}, ошибка: {{error}}", "closed": "Соединение с базой данных закрыто: {{database}}", "timeout": "Тайм-аут подключения к базе данных: {{database}}"}, "query": {"success": "Запрос к базе данных выполнен успешно, время: {{duration}}мс", "failed": "Ошибка запроса к базе данных: {{error}}", "slow": "Медленный запрос к базе данных, время: {{duration}}мс", "empty": "Запрос к базе данных вернул пустой результат"}, "migration": {"started": "Начата миграция базы данных: {{version}}", "completed": "Миграция базы данных завершена: {{version}}", "failed": "Ошибка миграции базы данных: {{error}}"}, "backup": {"started": "Начато резервное копирование базы данных", "completed": "Резервное копирование базы данных завершено: {{filename}}", "failed": "Ошибка резервного копирования базы данных: {{error}}"}}, "file": {"add": {"success": "Файл успешно добавлен: {{filename}}", "failed": "Ошибка добавления файла: {{filename}}, ошибка: {{error}}", "duplicate": "Файл уже существует: {{filename}}", "invalid": "Неверный путь к файлу: {{filename}}"}, "remove": {"success": "Файл успешно удален: {{filename}}", "failed": "Ошибка удаления файла: {{filename}}, ошибка: {{error}}", "notFound": "Файл не найден: {{filename}}"}, "launch": {"success": "Файл успешно запущен: {{filename}}, время: {{duration}}мс", "failed": "Ошибка запуска файла: {{filename}}, ошибка: {{error}}", "adminRequired": "Для запуска файла требуются права администратора: {{filename}}", "timeout": "Тайм-аут запуска файла: {{filename}}"}, "scan": {"started": "Начато сканирование файлов: {{directory}}", "completed": "Сканирование файлов завершено, найдено {{count}} файлов", "failed": "Ошибка сканирования файлов: {{error}}"}}, "ui": {"component": {"mounted": "Компонент подключен: {{component}}", "unmounted": "Компонент отключен: {{component}}", "rendered": "Компонент отрендерен: {{component}}, время: {{duration}}мс", "error": "Ошибка рендеринга компонента: {{component}}, ошибка: {{error}}"}, "theme": {"changed": "Тема успешно изменена: {{theme}}", "failed": "Ошибка изменения темы: {{error}}", "loaded": "Тема успешно загружена: {{theme}}", "reset": "Тема сброшена к настройкам по умолчанию"}, "navigation": {"changed": "Навигация по страницам: {{from}} -> {{to}}", "failed": "Ошибка навигации по страницам: {{error}}"}}, "i18n": {"language": {"changed": "Язык успешно изменен: {{from}} -> {{to}}", "failed": "Ошибка изменения языка: {{error}}", "detected": "Обнаружен системный язык: {{language}}", "fallback": "Используется резервный язык: {{language}}", "loading_saved": "Загрузка сохраненного языка: {{language}}", "load_failed": "Ошибка загрузки сохраненного языка, используется по умолчанию: {{error}}"}, "translation": {"loaded": "Файл переводов успешно загружен: {{language}}", "failed": "Ошибка загрузки файла переводов: {{language}}, ошибка: {{error}}", "missing": "Отсутствует ключ перевода: {{key}}", "cached": "Перевод кэширован: {{language}}", "file_missing": "Отсутствует файл переводов: {{filePath}}", "file_load_failed": "Ошибка загрузки файла переводов {{language}}/{{namespace}}: {{error}}", "resources_reload_failed": "Ошибка перезагрузки ресурсов переводов: {{error}}"}}, "ipc": {"message": {"sent": "IPC сообщение отправлено: {{channel}}", "received": "IPC сообщение получено: {{channel}}", "failed": "Ошибка обработки IPC сообщения: {{channel}}, ошибка: {{error}}", "timeout": "Тайм-аут IPC сообщения: {{channel}}"}, "handler": {"registered": "IPC обработчик зарегистрирован: {{channel}}", "unregistered": "IPC обработчик отменен: {{channel}}", "error": "Ошибка IPC обработчика: {{channel}}, ошибка: {{error}}"}}, "perf": {"startup": {"time": "Время запуска приложения: {{duration}}мс", "slow": "Медленный запуск приложения: {{duration}}мс"}, "memory": {"usage": "Использование памяти: {{used}}МБ / {{total}}МБ ({{percentage}}%)", "high": "Высокое использование памяти: {{used}}МБ", "gc": "Сборка мусора завершена, освобождено: {{freed}}МБ"}, "cpu": {"usage": "Использование ЦП: {{percentage}}%", "high": "Высокое использование ЦП: {{percentage}}%"}, "operation": {"completed": "Операция завершена: {{operation}}, время: {{duration}}мс", "slow": "Медленная операция: {{operation}}, время: {{duration}}мс"}}, "theme": {"load": {"success": "Тема успешно загружена: {{theme}}", "failed": "Ошибка загрузки темы: {{theme}}, ошибка: {{error}}"}, "apply": {"success": "Тема успешно применена: {{theme}}", "failed": "Ошибка применения темы: {{theme}}, ошибка: {{error}}"}, "export": {"success": "Тема успешно экспортирована: {{filename}}", "failed": "Ошибка экспорта темы: {{error}}"}, "import": {"success": "Тема успешно импортирована: {{filename}}", "failed": "Ошибка импорта темы: {{filename}}, ошибка: {{error}}"}}, "backup": {"create": {"success": "Резервная копия успешно создана: {{filename}}", "failed": "Ошибка создания резервной копии: {{error}}", "started": "Начинается создание резервной копии..."}, "restore": {"success": "Резервная копия успешно восстановлена: {{filename}}", "failed": "Ошибка восстановления резервной копии: {{filename}}, ошибка: {{error}}", "started": "Начинается восстановление резервной копии: {{filename}}"}, "cleanup": {"success": "Очистка резервных копий завершена, удалено {{count}} файлов", "failed": "Ошибка очистки резервных копий: {{error}}"}}, "system": {"error": {"uncaught": "Неперехваченное исключение: {{error}}", "unhandled": "Необработанное отклонение Promise: {{reason}}", "critical": "Критическая системная ошибка: {{error}}", "translation_failed": "Ошибка перевода сообщения журнала: {{key}}", "log_manager_internal": "Внутренняя ошибка менеджера журналов: {{error}}", "write_log_failed": "Ошибка записи журнала: {{error}}", "flush_buffer_failed": "Ошибка очистки буфера для {{filename}}: {{error}}"}, "resource": {"low_memory": "Недостаточно системной памяти: {{available}}МБ", "low_disk": "Недостаточно места на диске: {{available}}ГБ", "high_cpu": "Высокое использование ЦП: {{usage}}%"}, "info": {"renderer_loaded": "Скрипт рендерера загружен", "electron_api_available": "ElectronAPI доступен: {{available}}", "react_app_rendered": "React приложение успешно отрендерено", "react_app_failed": "Ошибка рендеринга React приложения: {{error}}", "app_mounted": "Компонент приложения смонтирован", "waiting_electron_api": "Ожидание ElectronAPI...", "electron_api_ready": "ElectronAPI готов", "app_initialization_completed": "Инициализация приложения завершена", "main_i18n_initialized": "MainI18n инициализирован с языком: {{language}}", "main_i18n_failed": "Ошибка инициализации MainI18n: {{error}}", "main_i18n_not_initialized": "MainI18n не инициализирован, возвращается значение по умолчанию", "main_i18n_resources_reloaded": "Ресурсы MainI18n перезагружены", "main_i18n_reload_failed": "Ошибка перезагрузки ресурсов MainI18n: {{error}}", "log_config_updated": "Конфигурация журнала обновлена"}, "navigation": {"changed": "Навигация по страницам: {{from}} -> {{to}}", "failed": "Ошибка навигации по страницам: {{error}}"}}, "logs_panel": {"error": {"load_failed": "Ошибка загрузки журналов", "export_failed": "Ошибка экспорта журналов"}, "info": {"system_ready": "Система журналирования готова", "logs_loaded": "Загружено {{count}} запи<PERSON><PERSON>й журнала", "export_success": "Журналы успешно экспортированы"}, "actions": {"refresh": "Обновить журналы", "export": "Экспортировать журналы"}, "ui": {"recent_logs": "Последние журналы"}}}
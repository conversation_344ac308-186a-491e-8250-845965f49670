{"general": {"unknown": "Unknown error", "networkError": "Network error", "serverError": "Server error", "clientError": "Client error", "timeout": "Request timeout", "cancelled": "Operation cancelled", "forbidden": "Access forbidden", "unauthorized": "Unauthorized access", "notFound": "Resource not found", "conflict": "Resource conflict", "tooManyRequests": "Too many requests", "internalError": "Internal error", "serviceUnavailable": "Service unavailable", "badGateway": "Bad gateway", "gatewayTimeout": "Gateway timeout"}, "file": {"notFound": "File not found", "accessDenied": "File access denied", "alreadyExists": "File already exists", "tooLarge": "File too large", "invalidFormat": "Invalid file format", "corrupted": "File corrupted", "readError": "File read error", "writeError": "File write error", "deleteError": "File delete error", "copyError": "File copy error", "moveError": "File move error", "renameError": "File rename error", "permissionDenied": "Insufficient file permissions", "diskFull": "Disk space full", "pathTooLong": "File path too long", "invalidPath": "Invalid file path", "locked": "File locked", "inUse": "File in use"}, "config": {"loadFailed": "Configuration load failed", "saveFailed": "Configuration save failed", "parseFailed": "Configuration parse failed", "validationFailed": "Configuration validation failed", "corrupted": "Configuration file corrupted", "notFound": "Configuration file not found", "accessDenied": "Configuration file access denied", "backupFailed": "Configuration backup failed", "restoreFailed": "Configuration restore failed", "migrationFailed": "Configuration migration failed", "versionMismatch": "Configuration version mismatch", "schemaError": "Configuration schema error", "defaultsError": "Default configuration error"}, "theme": {"loadFailed": "Theme load failed", "saveFailed": "Theme save failed", "parseFailed": "Theme parse failed", "invalidFormat": "Invalid theme format", "notFound": "Theme not found", "alreadyExists": "Theme already exists", "exportFailed": "Theme export failed", "importFailed": "Theme import failed", "applyFailed": "Theme apply failed", "resetFailed": "Theme reset failed", "colorInvalid": "Invalid color value", "fontNotFound": "Font not found", "effectNotSupported": "Effect not supported"}, "app": {"initFailed": "Application initialization failed", "startupError": "Startup error", "shutdownError": "Shutdown error", "updateFailed": "Update failed", "installFailed": "Installation failed", "uninstallFailed": "Uninstallation failed", "migrationFailed": "Data migration failed", "backupFailed": "Backup failed", "restoreFailed": "Rest<PERSON> failed", "syncFailed": "Sync failed", "authFailed": "Authentication failed", "licenseInvalid": "Invalid license", "versionIncompatible": "Incompatible version"}, "ipc": {"channelNotFound": "IPC channel not found", "handlerNotFound": "IPC handler not found", "messageInvalid": "Invalid IPC message", "timeoutError": "IPC timeout error", "connectionLost": "IPC connection lost", "permissionDenied": "IPC permission denied", "serializationError": "IPC serialization error", "deserializationError": "IPC deserialization error", "protocolError": "IPC protocol error", "bufferOverflow": "IPC buffer overflow"}, "database": {"connectionFailed": "Database connection failed", "queryFailed": "Database query failed", "transactionFailed": "Database transaction failed", "migrationFailed": "Database migration failed", "backupFailed": "Database backup failed", "restoreFailed": "Database restore failed", "corruptedData": "Corrupted database data", "schemaError": "Database schema error", "constraintViolation": "Database constraint violation", "deadlock": "Database deadlock", "timeout": "Database timeout", "diskFull": "Database disk full", "permissionDenied": "Database permission denied", "versionMismatch": "Database version mismatch"}, "network": {"connectionFailed": "Network connection failed", "requestFailed": "Network request failed", "responseInvalid": "Invalid network response", "timeout": "Network timeout", "offline": "Network offline", "dnsError": "DNS resolution error", "sslError": "SSL/TLS error", "proxyError": "Proxy error", "rateLimited": "Rate limited", "hostUnreachable": "Host unreachable", "portBlocked": "Port blocked", "protocolError": "Protocol error"}, "ui": {"renderFailed": "UI render failed", "componentError": "Component error", "eventHandlerError": "Event handler error", "stateUpdateError": "State update error", "routingError": "Routing error", "validationError": "Form validation error", "loadingError": "Loading error", "resourceNotFound": "UI resource not found"}, "actions": {"retry": "Retry", "contact": "Contact Support", "reload": "Reload", "restart": "Restart App", "reset": "Reset", "restore": "Rest<PERSON>"}}
/**
 * QuickStart 国际化日志记录器
 * 
 * 集成i18next系统，提供多语言日志记录功能
 */

import { LogManager } from './LogManager';
import { LogLevel, LogCategory, type LogSource, type SupportedLanguage } from './types';

/**
 * 国际化日志记录器
 */
export class I18nLogger {
  private logManager: LogManager;
  private currentLanguage: SupportedLanguage = 'zh-CN';
  private i18nFunction?: (key: string, fallback: string, params?: Record<string, unknown>) => string;

  constructor(source: LogSource, logManager?: LogManager) {
    this.logManager = logManager ?? LogManager.getInstance(source);
  }

  /**
   * 设置国际化函数
   */
  setI18nFunction(i18nFn: (key: string, fallback: string, params?: Record<string, unknown>) => string): void {
    this.i18nFunction = i18nFn;
    this.logManager.setI18nFunction(i18nFn);
  }

  /**
   * 设置当前语言
   */
  setLanguage(language: SupportedLanguage): void {
    this.currentLanguage = language;
    this.logManager.setLanguage(language);
  }

  /**
   * 记录应用程序启动成功
   */
  async logAppStartupSuccess(filename: string, version: string): Promise<void> {
    await this.logManager.logI18n(
      LogLevel.INFO,
      'logs.app.startup.success',
      `Application started successfully, version: ${version}`,
      LogCategory.APP,
      filename,
      { version }
    );
  }

  /**
   * 记录应用程序启动失败
   */
  async logAppStartupFailed(filename: string, error: Error): Promise<void> {
    await this.logManager.logI18n(
      LogLevel.ERROR,
      'logs.app.startup.failed',
      `Application startup failed: ${error.message}`,
      LogCategory.APP,
      filename,
      { error: error.message },
      undefined,
      error
    );
  }

  /**
   * 记录配置加载成功
   */
  async logConfigLoadSuccess(filename: string, configType: string): Promise<void> {
    await this.logManager.logI18n(
      LogLevel.INFO,
      'logs.config.load.success',
      `Configuration loaded successfully: ${configType}`,
      LogCategory.CONFIG,
      filename,
      { configType }
    );
  }

  /**
   * 记录配置加载失败
   */
  async logConfigLoadFailed(filename: string, configType: string, error: Error): Promise<void> {
    await this.logManager.logI18n(
      LogLevel.ERROR,
      'logs.config.load.failed',
      `Configuration load failed: ${configType}, error: ${error.message}`,
      LogCategory.CONFIG,
      filename,
      { configType, error: error.message },
      undefined,
      error
    );
  }

  /**
   * 记录数据库连接成功
   */
  async logDbConnectionSuccess(filename: string, database: string): Promise<void> {
    await this.logManager.logI18n(
      LogLevel.INFO,
      'logs.db.connection.success',
      `Database connected successfully: ${database}`,
      LogCategory.DB,
      filename,
      { database }
    );
  }

  /**
   * 记录数据库查询成功
   */
  async logDbQuerySuccess(filename: string, duration: number): Promise<void> {
    await this.logManager.logI18n(
      LogLevel.DEBUG,
      'logs.db.query.success',
      `数据库查询成功，耗时: ${duration}ms`,
      LogCategory.DB,
      filename,
      { duration }
    );
  }

  /**
   * 记录文件添加成功
   */
  async logFileAddSuccess(filename: string, addedFile: string): Promise<void> {
    await this.logManager.logI18n(
      LogLevel.INFO,
      'logs.file.add.success',
      `文件添加成功: ${addedFile}`,
      LogCategory.FILE,
      filename,
      { filename: addedFile }
    );
  }

  /**
   * 记录文件启动成功
   */
  async logFileLaunchSuccess(filename: string, launchedFile: string, duration: number): Promise<void> {
    await this.logManager.logI18n(
      LogLevel.INFO,
      'logs.file.launch.success',
      `文件启动成功: ${launchedFile}，耗时: ${duration}ms`,
      LogCategory.FILE,
      filename,
      { filename: launchedFile, duration }
    );
  }

  /**
   * 记录组件渲染完成
   */
  async logComponentRendered(filename: string, component: string, duration: number): Promise<void> {
    await this.logManager.logI18n(
      LogLevel.DEBUG,
      'logs.ui.component.rendered',
      `组件渲染完成: ${component}，耗时: ${duration}ms`,
      LogCategory.UI,
      filename,
      { component, duration }
    );
  }

  /**
   * 记录语言切换成功
   */
  async logLanguageChanged(filename: string, from: string, to: string): Promise<void> {
    await this.logManager.logI18n(
      LogLevel.INFO,
      'logs.i18n.language.changed',
      `语言切换成功: ${from} -> ${to}`,
      LogCategory.I18N,
      filename,
      { from, to }
    );
  }

  /**
   * 记录IPC消息发送
   */
  async logIpcMessageSent(filename: string, channel: string): Promise<void> {
    await this.logManager.logI18n(
      LogLevel.DEBUG,
      'logs.ipc.message.sent',
      `IPC消息发送: ${channel}`,
      LogCategory.IPC,
      filename,
      { channel }
    );
  }

  /**
   * 记录IPC消息接收
   */
  async logIpcMessageReceived(filename: string, channel: string): Promise<void> {
    await this.logManager.logI18n(
      LogLevel.DEBUG,
      'logs.ipc.message.received',
      `IPC消息接收: ${channel}`,
      LogCategory.IPC,
      filename,
      { channel }
    );
  }

  /**
   * 记录国际化日志 (直接访问底层方法)
   */
  async logI18n(
    level: LogLevel,
    i18nKey: string,
    fallbackMessage: string,
    category: LogCategory,
    filename: string,
    i18nParams?: Record<string, unknown>,
    data?: Record<string, unknown>,
    error?: Error
  ): Promise<void> {
    await this.logManager.logI18n(level, i18nKey, fallbackMessage, category, filename, i18nParams, data, error);
  }

  /**
   * 记录性能指标
   */
  async logPerformanceMetric(filename: string, operation: string, duration: number): Promise<void> {
    const level = duration > 1000 ? LogLevel.WARN : LogLevel.DEBUG;
    const key = duration > 1000 ? 'logs.perf.operation.slow' : 'logs.perf.operation.completed';
    const fallback = duration > 1000 
      ? `操作较慢: ${operation}，耗时: ${duration}ms`
      : `操作完成: ${operation}，耗时: ${duration}ms`;

    await this.logManager.logI18n(
      level,
      key,
      fallback,
      LogCategory.PERF,
      filename,
      { operation, duration }
    );
  }

  /**
   * 记录主题切换
   */
  async logThemeChanged(filename: string, theme: string): Promise<void> {
    await this.logManager.logI18n(
      LogLevel.INFO,
      'logs.theme.changed',
      `主题切换成功: ${theme}`,
      LogCategory.THEME,
      filename,
      { theme }
    );
  }

  /**
   * 记录备份创建成功
   */
  async logBackupCreated(filename: string, backupFilename: string): Promise<void> {
    await this.logManager.logI18n(
      LogLevel.INFO,
      'logs.backup.create.success',
      `备份创建成功: ${backupFilename}`,
      LogCategory.BACKUP,
      filename,
      { filename: backupFilename }
    );
  }

  /**
   * 记录系统错误
   */
  async logSystemError(filename: string, error: Error): Promise<void> {
    await this.logManager.logI18n(
      LogLevel.ERROR,
      'logs.system.error.uncaught',
      `未捕获的异常: ${error.message}`,
      LogCategory.APP,
      filename,
      { error: error.message },
      undefined,
      error
    );
  }

  /**
   * 直接访问底层LogManager的方法
   */
  async trace(message: string, category: LogCategory, filename: string, data?: Record<string, unknown>, error?: Error): Promise<void> {
    await this.logManager.trace(message, category, filename, data, error);
  }

  async debug(message: string, category: LogCategory, filename: string, data?: Record<string, unknown>, error?: Error): Promise<void> {
    await this.logManager.debug(message, category, filename, data, error);
  }

  async info(message: string, category: LogCategory, filename: string, data?: Record<string, unknown>, error?: Error): Promise<void> {
    await this.logManager.info(message, category, filename, data, error);
  }

  async warn(message: string, category: LogCategory, filename: string, data?: Record<string, unknown>, error?: Error): Promise<void> {
    await this.logManager.warn(message, category, filename, data, error);
  }

  async error(message: string, category: LogCategory, filename: string, data?: Record<string, unknown>, error?: Error): Promise<void> {
    await this.logManager.error(message, category, filename, data, error);
  }

  async fatal(message: string, category: LogCategory, filename: string, data?: Record<string, unknown>, error?: Error): Promise<void> {
    await this.logManager.fatal(message, category, filename, data, error);
  }

  /**
   * 获取底层LogManager实例
   */
  getLogManager(): LogManager {
    return this.logManager;
  }
}

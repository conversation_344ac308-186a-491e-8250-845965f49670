---
description: 当开发者输入"/问题"指令时使用
globs: 
alwaysApply: false
---
# 问题解决处理规范

## 使用场景
- 用户输入`/问题`指令时
- 遇到编程错误需要解决时
- 需要技术问题分析和修复时

## 关键规则
- 深入分析问题的根本原因
- 提供多种解决方案供选择
- 实施修复方案并验证结果
- 在相关模块文档中记录错误和解决过程
- 预防类似问题的复发

## 问题解决流程
1. **问题理解** → 2. **原因分析** → 3. **方案设计** → 4. **解决实施** → 5. **结果验证** → 6. **文档记录**

## 问题分类
- **功能性错误**: 代码逻辑问题、算法错误
- **集成错误**: 模块间接口不匹配、数据传递问题
- **环境错误**: 配置问题、依赖冲突
- **性能问题**: 响应慢、资源占用高

## 示例

<example>
```text
用户：/问题 登录功能报500错误

AI回复：
🔍 分析登录功能500错误...

📋 问题诊断：
1. 检查前端登录请求格式 ✅
2. 检查后端API接口 ❌ 发现问题
3. 检查数据库连接 ✅
4. 检查错误日志 ✅

🎯 问题定位：
- 文件：backend/auth.py 第23行
- 原因：用户密码验证时缺少异常处理
- 影响：所有登录请求都会触发500错误

🔧 解决方案：
1. 添加try-catch异常处理
2. 完善错误返回信息
3. 添加日志记录

正在实施修复...
✅ 已修复backend/auth.py
✅ 已更新docs/auth.md错误记录
✅ 已测试验证修复效果

问题解决完成！登录功能已恢复正常。
```
</example>

<example type="invalid">
```text
"这个问题很复杂，建议重新写代码。"
（错误：没有具体分析，没有定位问题，没有提供解决方案）
```

</example>
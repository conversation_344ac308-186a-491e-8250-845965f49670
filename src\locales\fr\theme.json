{
  "title": "Paramètres de thème",
  "presets": {
    "iosBlue": "iOS Bleu",
    "macosGreen": "macOS Vert",
    "appleOrange": "Apple Orange",
    "deepSpaceGray": "Gris espace profond",
    "midnightBlue": "Bleu minuit"
  },
  "colors": {
    "title": "Couleurs d'interface",
    "primary": "Couleur principale",
    "secondary": "Couleur secondaire",
    "background": "Couleur d'arrière-plan",
    "surface": "Couleur de surface",
    "text": "Couleur du texte",
    "textSecondary": "Couleur du texte secondaire",
    "border": "Couleur de bordure",
    "success": "Couleur de succès",
    "warning": "Couleur d'avertissement",
    "error": "Couleur d'erreur",
    "info": "Couleur d'information",
    "accent": "Couleur d'accent",
    "muted": "Couleur atténuée",
    "highlight": "Couleur de surbrillance",
    "shadow": "Couleur d'ombre",
    "interface": "Couleurs d'interface",
    "labels": {
      "surface": "Couleur de surface",
      "text": "Couleur du texte",
      "textSecondary": "Texte secondaire",
      "border": "Couleur de bordure",
      "shadow": "Couleur d'ombre"
    },
    "descriptions": {
      "surface": "Couleur d'arrière-plan pour les cartes et panneaux",
      "text": "Couleur principale du texte",
      "textSecondary": "Couleur du texte secondaire et des descriptions",
      "border": "Couleur des bordures et séparateurs",
      "shadow": "Couleur des ombres et ombres portées"
    }
  },
  "fonts": {
    "title": "Paramètres de police",
    "family": "Famille de police",
    "size": "Taille de police",
    "weight": "Épaisseur de police",
    "lineHeight": "Hauteur de ligne",
    "letterSpacing": "Espacement des lettres",
    "systemFont": "Police système",
    "customFont": "Police personnalisée",
    "configuration": "Configuration de police",
    "options": {
      "appleSystem": "Police système Apple",
      "microsoftYaHei": "Microsoft YaHei",
      "pingFangSC": "PingFang SC",
      "helveticaNeue": "Helvetica Neue",
      "sfProDisplay": "SF Pro Display",
      "segoeUI": "Segoe UI"
    },
    "weights": {
      "thin": "Fin",
      "light": "Léger",
      "regular": "Normal",
      "medium": "Moyen",
      "semibold": "Semi-gras",
      "bold": "Gras",
      "heavy": "Lourd"
    }
  },
  "effects": {
    "title": "Effets visuels",
    "glassEffect": "Effet de verre dépoli",
    "transparency": "Transparence",
    "blur": "Flou",
    "shadows": "Effets d'ombre",
    "animations": "Effets d'animation",
    "transitions": "Effets de transition",
    "borderRadius": "Rayon de bordure",
    "gradient": "Effets de dégradé",
    "compactMode": "Mode compact"
  },
  "background": {
    "title": "Paramètres d'arrière-plan",
    "type": "Type d'arrière-plan",
    "color": "Arrière-plan couleur unie",
    "gradient": "Arrière-plan dégradé",
    "image": "Arrière-plan image",
    "pattern": "Arrière-plan motif",
    "opacity": "Opacité de l'arrière-plan",
    "position": "Position de l'arrière-plan",
    "size": "Taille de l'arrière-plan",
    "repeat": "Répétition de l'arrière-plan",
    "attachment": "Attachement de l'arrière-plan",
    "blend": "Mode de fusion",
    "enabled": "Activer l'arrière-plan",
    "basicSettings": "Paramètres de base",
    "colorSettings": "Paramètres de couleur",
    "gradientSettings": "Paramètres de dégradé",
    "imageSettings": "Paramètres d'image",
    "reset": "Réinitialiser les paramètres",
    "gradientType": "Type de dégradé",
    "gradientDirection": "Direction du dégradé",
    "gradientColors": "Couleurs du dégradé",
    "startColor": "Début",
    "endColor": "Fin",
    "colorStop": "Couleur",
    "addColor": "Ajouter couleur",
    "removeColor": "Supprimer couleur",
    "noGradientColors": "Aucune couleur de dégradé",
    "clickAddColor": "Cliquez sur \"Ajouter couleur\" pour ajouter des couleurs de dégradé",
    "gradientTypes": {
      "linear": "Dégradé linéaire",
      "radial": "Dégradé radial"
    },
    "types": {
      "none": "Pas d'arrière-plan",
      "color": "Couleur unie",
      "gradient": "Dégradé",
      "image": "Image"
    },
    "performanceSettings": "Paramètres de performance",
    "performanceTip": "Conseil de performance",
    "performanceDesc": "Les images d'arrière-plan haute résolution peuvent affecter les performances de l'application. Il est recommandé d'utiliser des paramètres de compression appropriés.",
    "localImage": "Image locale",
    "networkImage": "Image réseau",
    "dragOrClick": "Cliquez ou faites glisser l'image dans cette zone pour télécharger",
    "supportFormats": "Prend en charge les formats JPG, PNG, GIF, WebP, maximum 50MB",
    "compressionTip": "Conseil de compression",
    "compressionDesc": "Les grandes images seront automatiquement compressées pour optimiser les performances tout en maintenant de bons effets visuels.",
    "displayMode": "Mode d'affichage",
    "blur": "Flou",
    "brightness": "Luminosité",
    "contrast": "Contraste",
    "saturation": "Saturation",
    "modes": {
      "stretch": "Étirer",
      "tile": "Mosaïque",
      "center": "Centrer",
      "cover": "Couvrir",
      "contain": "Contenir"
    },
    "processing": "Traitement de l'image...",
    "urlLoadSuccess": "Image réseau chargée avec succès",
    "urlLoadFailed": "Échec du chargement de l'image réseau",
    "removeSuccess": "Image d'arrière-plan supprimée",
    "preview": "Aperçu",
    "viewFullSize": "Voir en taille réelle",
    "removeImage": "Supprimer l'image",
    "loadError": "Échec du chargement de la configuration d'arrière-plan",
    "noConfig": "Configuration d'arrière-plan non disponible",
    "configWillBeCreated": "La configuration sera créée lors de la première utilisation",
    "urlImage": "Image réseau",
    "enterImageUrl": "Entrez l'URL de l'image (https://...)",
    "urlTip": "Conseil pour les images réseau",
    "urlDesc": "Veuillez vous assurer que le lien de l'image est accessible et prend en charge le protocole HTTPS.",
    "enterUrl": "Veuillez entrer le lien de l'image",
    "uploadSuccess": "Image d'arrière-plan téléchargée avec succès",
    "uploadFailed": "Échec du téléchargement de l'image d'arrière-plan",
    "downloadingImage": "Téléchargement de l'image réseau...",
    "processingLocalImage": "Traitement de l'image locale...",
    "compressingImage": "Compression de l'image pour optimiser les performances...",
    "savingImage": "Sauvegarde de l'image d'arrière-plan...",
    "imageSetSuccess": "Image d'arrière-plan définie avec succès",
    "imageSetFailed": "Échec de la définition de l'image d'arrière-plan",
    "usingMemoryMode": "Utilisation du mode mémoire pour définir l'arrière-plan (nécessite une réinitialisation après redémarrage)",
    "imageSetSuccessMemory": "Image d'arrière-plan définie avec succès (mode temporaire)",
    "imageSetFailedComplete": "La définition de l'image d'arrière-plan a complètement échoué",
    "defaultImageName": "Image d'arrière-plan",
    "duplicateFileName": "Nom de fichier en double",
    "duplicateFileContent": "Le nom de fichier \"{{fileName}}\" existe déjà, renommer et sauvegarder ?",
    "renameAndSave": "Renommer et sauvegarder",
    "cancelSave": "Annuler la sauvegarde de l'image d'arrière-plan",
    "permissionDeniedError": "Permissions insuffisantes pour le répertoire de cache, veuillez vérifier les permissions de l'application",
    "diskSpaceError": "Espace disque insuffisant, impossible de sauvegarder l'image d'arrière-plan",
    "cacheNotInitializedError": "Système de cache non initialisé, nouvelle tentative...",
    "imageCacheFailedError": "Échec du cache d'image : {{message}}",
    "imageValidation": {
      "unsupportedFormat": "Format d'image non pris en charge : {{format}}. Formats pris en charge : JPEG, PNG, GIF, WebP",
      "fileTooLarge": "Fichier image trop volumineux : {{size}}MB. Maximum pris en charge : 50MB",
      "fileInvalid": "Objet fichier invalide",
      "emptyFile": "La taille du fichier est de 0, il s'agit peut-être d'un fichier vide",
      "invalidFileType": "Type de fichier invalide : {{type}}, type d'image attendu",
      "loadTimeout": "Délai d'attente de chargement d'image (5 secondes), le fichier peut être corrompu ou trop volumineux",
      "readTimeout": "Délai d'attente de lecture du fichier (10 secondes), le fichier peut être trop volumineux ou corrompu",
      "invalidDimensions": "Dimensions d'image invalides, le fichier peut être corrompu ou ne pas être un format d'image valide",
      "cannotReadFile": "Impossible de lire le fichier image",
      "jpegCorrupted": "Le fichier JPEG existe mais peut être corrompu, veuillez essayer de le sauvegarder à nouveau avec un logiciel d'édition d'images",
      "pngCorrupted": "Le fichier PNG existe mais peut être corrompu, veuillez essayer de le sauvegarder à nouveau avec un logiciel d'édition d'images",
      "gifCorrupted": "Le fichier GIF existe mais peut être corrompu, veuillez essayer de le sauvegarder à nouveau avec un logiciel d'édition d'images",
      "webpCorrupted": "Le fichier WebP existe mais peut être corrompu ou non pris en charge dans l'environnement actuel",
      "bmpCorrupted": "Le fichier BMP existe mais peut être corrompu, il est recommandé de le convertir au format JPG ou PNG",
      "webpNotSupported": "Le format WebP n'est pas pris en charge dans le navigateur actuel, veuillez utiliser le format JPG ou PNG",
      "fileCorruptedGeneric": "Le fichier n'est pas un format d'image valide ou est corrompu. En-tête du fichier : {{header}}...",
      "unsupportedFileType": "Type de fichier non pris en charge : {{type}}. Veuillez sélectionner un fichier image valide (JPG, PNG, GIF, WebP)",
      "fileTooLargeSimple": "Fichier image trop volumineux, veuillez sélectionner une image de moins de 50MB",
      "corruptionSuggestions": "Causes possibles : le fichier peut être corrompu ; le format d'image peut ne pas être pris en charge ; les permissions de fichier peuvent être problématiques"
    },
    "imageProcessing": {
      "timeout": "Délai d'attente de traitement d'image (15 secondes), l'image peut être trop volumineuse",
      "cannotCreateCanvas": "Impossible de créer le contexte Canvas",
      "cannotGeneratePreview": "Impossible de générer l'aperçu de l'image",
      "processingFailed": "Échec du traitement de l'image : {{message}}",
      "cannotLoadForProcessing": "Impossible de charger l'image pour le traitement",
      "batchProcessingFailed": "Échec du traitement de l'image {{fileName}}"
    },
    "imageNetwork": {
      "downloadTimeout": "Délai d'attente de téléchargement d'image réseau : {{seconds}} secondes",
      "loadTimeout": "Délai d'attente de chargement d'image réseau : {{seconds}} secondes",
      "corsError": "Erreur CORS : Impossible d'accéder à cette image, le serveur n'autorise pas les requêtes cross-origin",
      "networkError": "Erreur réseau : Veuillez vérifier la connexion réseau et la validité du lien de l'image",
      "downloadFailed": "Échec du téléchargement de l'image réseau : {{message}}",
      "httpError": "HTTP {{status}} : {{statusText}}",
      "invalidContentType": "Type de contenu invalide : {{contentType}}. Type d'image attendu.",
      "invalidUrl": "Format de lien d'image invalide",
      "protocolNotSupported": "Seuls les liens d'images des protocoles HTTP et HTTPS sont pris en charge",
      "invalidExtension": "Le lien de l'image doit se terminer par un format pris en charge (.jpg, .jpeg, .png, .gif, .webp)"
    },
    "cache": {
      "initializationFailed": "L'initialisation du gestionnaire de cache a échoué : {{message}}",
      "permissionDeniedCreate": "Permission refusée : Impossible de créer ou d'écrire dans le répertoire de cache : {{dir}}. Veuillez vérifier les permissions du dossier.",
      "insufficientDiskSpaceCreate": "Espace disque insuffisant pour créer le répertoire de cache : {{dir}}",
      "invalidPathCreate": "Chemin invalide : Un fichier existe là où le répertoire devrait être créé : {{dir}}",
      "ensureCacheDirectoryFailed": "Échec de l'assurance du répertoire de cache : {{message}}",
      "insufficientDiskSpaceCache": "Espace disque insuffisant : {{required}}MB requis, {{available}}MB disponible",
      "fileWriteVerificationFailed": "La vérification d'écriture du fichier a échoué : {{expected}} octets attendus, {{actual}} octets obtenus",
      "insufficientDiskSpace": "Espace disque insuffisant pour mettre en cache l'image",
      "permissionDeniedWrite": "Permission refusée : Impossible d'écrire dans le répertoire de cache : {{dir}}",
      "tooManyOpenFiles": "Trop de fichiers ouverts : Impossible de mettre en cache l'image",
      "cacheImageFailed": "Échec de la mise en cache de l'image : {{message}}",
      "cleanupPartialFileFailed": "Échec du nettoyage du fichier partiel"
    },
    "cachedImages": "Images en cache",
    "noCachedImages": "Aucune image en cache",
    "cachedImagesDesc": "Cliquez sur l'image pour changer l'arrière-plan, cliquez sur le bouton supprimer pour retirer du cache",
    "switchToImage": "Basculer vers cet arrière-plan",
    "removeFromCache": "Supprimer du cache",
    "confirmRemoveCache": "Confirmer la suppression du cache",
    "confirmRemoveCacheContent": "Êtes-vous sûr de vouloir supprimer l'image \"{{fileName}}\" du cache ? Cette action ne peut pas être annulée.",
    "removeCacheSuccess": "Image supprimée du cache",
    "removeCacheFailed": "Échec de la suppression de l'image en cache",
    "imageCache": "Cache d'images",
    "cacheManagement": "Gestion du cache",
    "customFileName": "Nom de fichier personnalisé",
    "enterCustomFileName": "Veuillez saisir un nom de fichier personnalisé",
    "fileNameInvalid": "Nom de fichier invalide",
    "renameImage": "Renommer l'image",
    "newFileName": "Nouveau nom de fichier",
    "renameSuccess": "Renommage réussi",
    "renameFailed": "Échec du renommage",
    "enterNewFileName": "Veuillez saisir le nouveau nom de fichier",
    "imageNetwork": {
      "downloadTimeoutRetry": "Délai d'attente de téléchargement d'image réseau, veuillez vérifier la connexion réseau ou réessayer plus tard",
      "corsAccessDenied": "Impossible d'accéder à cette image, le serveur n'autorise pas les requêtes cross-origin",
      "networkConnectionError": "Erreur de connexion réseau, veuillez vérifier les paramètres réseau",
      "serverError": "Erreur serveur: {{message}}",
      "fileTooLargeNetwork": "Fichier d'image réseau trop volumineux",
      "invalidImageLink": "Le lien n'est pas un fichier image valide",
      "genericError": "{{baseMessage}}: {{details}}"
      "fileTooLargeNetwork": "Fichier d'image réseau trop volumineux",
      "invalidImageLink": "Le lien n'est pas un fichier d'image valide"
    },
    "imageProcessing": {
      "setImageSourceFailed": "Échec de la définition de la source d'image: {{message}}",
      "fileReadFailed": "Échec de la lecture du fichier: {{message}}",
      "unknownError": "Erreur inconnue"
    }
  },
  "actions": {
    "export": "Exporter le thème",
    "import": "Importer le thème",
    "reset": "Réinitialiser le thème"
  },
  "messages": {
    "themeExported": "Configuration du thème exportée",
    "themeImported": "Configuration du thème importée",
    "themeImportError": "Erreur de format du fichier de configuration du thème",
    "layoutUpdated": "Paramètres de mise en page mis à jour",
    "layoutUpdateFailed": "Échec de la mise à jour des paramètres de mise en page",
    "layoutUpdateError": "Erreur lors de la mise à jour des paramètres de mise en page",
    "loadingLayout": "Chargement des paramètres de mise en page...",
    "loadLayoutFailed": "Échec du chargement des paramètres de mise en page",
    "noLayoutConfig": "Impossible de charger les paramètres de mise en page",
    "checkConfigSystem": "Vérifiez si le système de configuration fonctionne normalement"
  },
  "layout": {
    "title": "Paramètres de mise en page",
    "sidebar": "Barre latérale",
    "sidebarWidth": "Largeur de la barre latérale",
    "sidebarPosition": "Position de la barre latérale"
  },
  "global": {
    "title": "Thème global",
    "primaryColor": "Couleur principale",
    "primaryColorDesc": "Affecte la couleur des boutons, liens et autres éléments principaux",
    "mode": "Mode d'affichage",
    "darkMode": "Mode sombre",
    "modeDesc": "Basculer entre les modes d'affichage clair et sombre",
    "font": "Paramètres de police",
    "fontSize": "Taille de police",
    "borderRadius": "Rayon de bordure"
  },
  "ui": {
    "title": "Paramètres de style",
    "description": "Personnaliser l'apparence et le thème de l'application",
    "interfaceColors": "Couleurs de l'interface",
    "themePresets": "Préréglages de thème",
    "typography": "Typographie",
    "visualEffects": "Effets visuels",
    "layoutSettings": "Paramètres de mise en page",
    "primaryColorDesc": "Utilisé pour les boutons, liens et autres éléments principaux",
    "colorLabels": {
      "background": "Arrière-plan",
      "surface": "Surface",
      "text": "Texte",
      "textSecondary": "Texte secondaire",
      "border": "Bordure",
      "shadow": "Ombre"
    },
    "fontSize": "Taille de police",
    "interfaceEffects": "Effets d'interface",
    "sidebar": {
      "title": "Paramètres de la barre latérale",
      "position": "Position de la barre latérale",
      "width": "Largeur de la barre latérale",
      "collapsed": "Réduit par défaut",
      "autoHide": "Masquage automatique",
      "positions": {
        "left": "Gauche",
        "right": "Droite"
      }
    },
    "fileList": {
      "title": "Paramètres de la liste de fichiers",
      "showSize": "Afficher la taille",
      "showModifiedTime": "Afficher l'heure de modification",
      "showAddedTime": "Afficher l'heure d'ajout",
      "showLaunchCount": "Afficher le nombre de lancements"
    },
    "statusBar": {
      "title": "Paramètres de la barre d'état",
      "visible": "Afficher la barre d'état",
      "showFileCount": "Afficher le nombre de fichiers",
      "showPath": "Afficher le chemin"
    }
  },
  "actions": {
    "apply": "Appliquer le thème",
    "reset": "Réinitialiser le thème",
    "customize": "Personnaliser le thème",
    "duplicate": "Dupliquer le thème",
    "rename": "Renommer le thème",
    "share": "Partager le thème"
  },
  "messages": {
    "themeApplied": "Thème appliqué",
    "themeReset": "Thème réinitialisé",
    "themeSaved": "Thème sauvegardé",
    "themeLoaded": "Thème chargé",
    "themeExported": "Configuration du thème exportée",
    "themeImported": "Configuration du thème importée",
    "themeImportError": "Erreur de format du fichier de configuration du thème",
    "layoutUpdated": "Paramètres de mise en page mis à jour",
    "layoutUpdateFailed": "Échec de la mise à jour des paramètres de mise en page",
    "layoutUpdateError": "Erreur lors de la mise à jour des paramètres de mise en page",
    "loadingLayout": "Chargement des paramètres de mise en page...",
    "loadLayoutFailed": "Échec du chargement des paramètres de mise en page",
    "noLayoutConfig": "Impossible de charger les paramètres de mise en page",
    "checkConfigSystem": "Veuillez vérifier si le système de configuration fonctionne correctement",
    "invalidThemeFile": "Fichier de thème invalide",
    "themeNameRequired": "Veuillez saisir le nom du thème",
    "themeNameExists": "Le nom du thème existe déjà",
    "confirmDeleteTheme": "Confirmer la suppression de ce thème ?",
    "confirmResetTheme": "Confirmer la réinitialisation des paramètres du thème ?",
    "themeDeleteConfirm": "Confirmer la suppression de ce thème ?",
    "themeResetConfirm": "Confirmer la réinitialisation du thème par défaut ?",
    "customThemeNameRequired": "Le nom du thème personnalisé est requis",
    "customThemeNameExists": "Le nom du thème personnalisé existe déjà",
    "themeFileInvalid": "Format de fichier de thème invalide",
    "themeLoadError": "Erreur de chargement du thème",
    "themeSaveError": "Erreur de sauvegarde du thème",
    "previewMode": "Mode aperçu activé",
    "previewModeDisabled": "Mode aperçu désactivé",
    "colorPickerTitle": "Sélectionner la couleur",
    "resetToDefault": "Réinitialiser par défaut",
    "importTheme": "Importer le thème",
    "exportTheme": "Exporter le thème",
    "themePreview": "Aperçu du thème",
    "applyChanges": "Appliquer les modifications",
    "discardChanges": "Annuler les modifications",
    "unsavedChanges": "Vous avez des modifications de thème non sauvegardées"
  }
}
---
description: 
globs: docs/*.md,docs/**/*.md
alwaysApply: false
---
# 模块化文档管理规范

## 使用场景
- 处理docs/模块化文件夹中的任何文档时
- 创建、更新或修改模块目录下的文档时
- 记录功能状态和错误追踪时

## 关键规则
- 每个功能模块都有独立的目录和技术文档
- 模块主文档命名为README.md，专项文档使用描述性名称
- 模块文档必须包含：功能概述、核心功能、技术实现、错误追踪、维护指南
- 错误追踪使用格式：`ERR-<模块>-<日期>-<序号>`
- 修复完成后完全删除错误记录，不保留历史报告
- 新增功能时同步更新对应模块目录下的文档
- 文档重点关注当前状态，避免冗长历史记录
- 相关文档集中在同一模块目录下管理

## 文档结构模板
```markdown
# 模块名称

## 功能概述
简要描述模块的主要职责和功能

## 核心功能
- ✅ 已完成功能
- 🔄 开发中功能
- ❌ 待开发功能

## 技术实现
技术栈、架构设计、关键算法等

## 错误追踪
### 🔴 错误：ERR-模块-日期-序号
**时间**: 日期
**描述**: 问题描述
**影响**: 影响范围
**状态**: 状态

## 维护指南
日常维护、更新和扩展指导
```

## 错误追踪管理
- 发现问题时：添加详细错误信息，更新功能状态
- 修复完成时：完全删除错误记录，恢复正常状态
- 无错误时显示："*当前无错误记录*"

## 示例

<example>
```markdown
# 用户认证模块

## 功能概述
负责用户登录、注册、权限验证等功能

## 核心功能
- ✅ 用户登录
- ✅ 用户注册
- 🔄 密码重置（开发中）

## 错误追踪
### 🔴 错误：ERR-AUTH-20250602-01
**时间**: 2025-06-02
**描述**: 登录接口返回500错误
**影响**: 用户无法正常登录
**状态**: 修复中
```
</example>

<example type="invalid">
```markdown
# 历史修复记录
- 2025-05-01: 修复了某个问题
- 2025-05-15: 修复了另一个问题
（冗长的历史记录，应该避免）
```

</example>
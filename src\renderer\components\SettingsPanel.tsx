import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Typography,
  Switch,

  Space,
  Divider,
  message,

  InputNumber,
  Tabs,
  Spin,
  Alert
} from 'antd';
import {
  SettingOutlined,
  GlobalOutlined,
  RocketOutlined,
  SafetyOutlined,
  ThunderboltOutlined,
  ToolOutlined,
  UserOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useI18nContext } from '../../contexts/I18nContext';
import { useTheme } from '../contexts/ThemeContext';
import { useAppSettings, useUserPreferences, useLayoutConfig } from '../hooks/useConfig';
import { ConfigPanel } from './ConfigPanel';
import { LanguageSelector } from './LanguageSelector';
import type { AppSettingsSchema, UserPreferencesSchema } from '@shared/config-schemas';

const { Title, Text } = Typography;


export const SettingsPanel: React.FC = () => {
  const { t } = useTranslation(['settings', 'common']);
  const { themeConfig } = useTheme();
  const { currentLanguage } = useI18nContext();
  const { config: appSettings, updateConfig: updateAppSettings, loading: appSettingsLoading, error: appSettingsError } = useAppSettings();
  const { config: userPreferences, updateConfig: updateUserPreferences, loading: userPreferencesLoading, error: userPreferencesError } = useUserPreferences();
  const { config: layoutConfig, updateConfig: updateLayoutConfig, loading: layoutLoading, error: layoutError } = useLayoutConfig();
  const [activeTab, setActiveTab] = useState('general');

  // Handle setting changes - save to config system in real time
  const handleSettingChange = async (settingPath: string, value: unknown, configType: 'app-settings' | 'user-preferences' | 'layout-config' = 'app-settings') => {
    try {
      const currentConfig = configType === 'app-settings' ? appSettings :
                           configType === 'user-preferences' ? userPreferences : layoutConfig;
      const updateFunction = configType === 'app-settings' ? updateAppSettings :
                            configType === 'user-preferences' ? updateUserPreferences : updateLayoutConfig;

      if (!currentConfig) {
        console.error(`${configType} not loaded`);
        return;
      }

      // Build update object based on setting path, preserving existing config structure
      const pathParts = settingPath.split('.');
      const updateObj: Partial<AppSettingsSchema | UserPreferencesSchema> = {};

      if (pathParts.length === 1) {
        // Top-level property
        (updateObj as Record<string, unknown>)[pathParts[0]] = value;
      } else if (pathParts.length === 2) {
        // Second-level property, need to preserve other properties at the same level
        const [category, property] = pathParts;
        const currentCategory = (currentConfig as unknown as Record<string, unknown>)[category];
        (updateObj as Record<string, unknown>)[category] = {
          ...(typeof currentCategory === 'object' && currentCategory !== null ? currentCategory : {}),
          [property]: value // Only update specified property
        };
      } else {
        // Deeper level nesting (if needed)
        console.warn('Deep nested settings not implemented:', settingPath);
        return;
      }

      console.log('Updating setting:', settingPath, '=', value, 'in', configType);
      console.log('Update object:', updateObj);

      // Save to config system
      const success = await updateFunction(updateObj);

      if (success) {
        message.success(t('common:messages.settingUpdated', '设置已更新'));
      } else {
        message.error(t('common:messages.settingUpdateFailed', '设置更新失败'));
      }
    } catch (error) {
      console.error('Failed to update setting:', error);
      message.error(t('common:messages.settingUpdateError', '设置更新错误'));
    }
  };

  // General settings component
  const GeneralSettings = () => {
    // Show loading state
    if (appSettingsLoading || userPreferencesLoading) {
      return (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">{t('settings:messages.loadingSettings', '正在加载设置...')}</Text>
          </div>
        </div>
      );
    }

    // Show error state
    if (appSettingsError || userPreferencesError) {
      return (
        <Alert
          message={t('settings:messages.loadSettingsFailed', '加载设置失败')}
          description={appSettingsError ?? userPreferencesError}
          type="error"
          showIcon
        />
      );
    }

    // If no config data, show warning
    if (!appSettings || !userPreferences) {
      return (
        <Alert
          message={t('settings:messages.cannotLoadSettings', '无法加载设置')}
          description={t('settings:messages.checkConfigSystem', '请检查配置系统')}
          type="warning"
          showIcon
        />
      );
    }

    return (
      <div>
        <div style={{ marginBottom: 24 }}>
          <Title level={3} style={{ margin: 0 }}>
            {t('settings:title', '设置')}
          </Title>
          <Text type="secondary">
            {t('settings:general.title', '常规设置')}
          </Text>
        </div>

        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* Window Settings */}
          <Card size="small" title={<><SettingOutlined /> {t('settings:window.title', '窗口设置')}</>}>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:window.maximizeOnStart', '启动时最大化')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={appSettings.window?.maximized || false}
                    onChange={(checked) => handleSettingChange('window.maximized', checked)}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:window.maximizeOnStartDesc', '启动时自动最大化窗口')}
                  </Text>
                </Col>
              </Row>

              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:window.alwaysOnTop', '窗口置顶')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={appSettings.window?.alwaysOnTop || false}
                    onChange={(checked) => handleSettingChange('window.alwaysOnTop', checked)}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:window.alwaysOnTopDesc', '保持窗口在最前面')}
                  </Text>
                </Col>
              </Row>
            </Space>
          </Card>

          {/* Startup Settings */}
          <Card size="small" title={<><RocketOutlined /> {t('settings:startup.title', '启动设置')}</>}>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:startup.autoLaunch', '开机自启')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={appSettings.startup?.autoLaunch || false}
                    onChange={(checked) => handleSettingChange('startup.autoLaunch', checked)}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:startup.autoLaunchDesc', '系统启动时自动运行')}
                  </Text>
                </Col>
              </Row>

              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:startup.minimizeToTray', '最小化到托盘')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={appSettings.startup?.minimizeToTray || false}
                    onChange={(checked) => handleSettingChange('startup.minimizeToTray', checked)}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:startup.minimizeToTrayDesc', '关闭窗口时最小化到系统托盘')}
                  </Text>
                </Col>
              </Row>

              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:startup.showSplashScreen', '显示启动画面')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={appSettings.startup?.showSplashScreen || false}
                    onChange={(checked) => handleSettingChange('startup.showSplashScreen', checked)}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:startup.showSplashScreenDesc', '启动时显示欢迎界面')}
                  </Text>
                </Col>
              </Row>

              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:startup.checkUpdates', '检查更新')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={appSettings.startup?.checkForUpdates || false}
                    onChange={(checked) => handleSettingChange('startup.checkForUpdates', checked)}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:startup.checkUpdatesDesc', '启动时自动检查软件更新')}
                  </Text>
                </Col>
              </Row>
            </Space>
          </Card>



          {/* Performance Settings */}
          <Card size="small" title={<><ThunderboltOutlined /> {t('settings:performance.title', '性能设置')}</>}>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:performance.hardwareAcceleration', '硬件加速')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={appSettings.performance?.enableHardwareAcceleration || false}
                    onChange={(checked) => handleSettingChange('performance.enableHardwareAcceleration', checked)}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:performance.hardwareAccelerationDesc', '启用GPU硬件加速')}
                  </Text>
                </Col>
              </Row>

              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:performance.cacheSize', '缓存大小')}:</Text>
                </Col>
                <Col span={16}>
                  <InputNumber
                    min={50}
                    max={500}
                    value={appSettings.performance?.maxCacheSize || 100}
                    onChange={(value) => handleSettingChange('performance.maxCacheSize', value)}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:performance.cacheSizeDesc', '设置应用缓存大小')}
                  </Text>
                </Col>
              </Row>

              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:performance.enableVirtualization', '启用虚拟化')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={appSettings.performance?.enableVirtualization || false}
                    onChange={(checked) => handleSettingChange('performance.enableVirtualization', checked)}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:performance.enableVirtualizationDesc', '提高大列表渲染性能')}
                  </Text>
                </Col>
              </Row>
            </Space>
          </Card>





        </Space>
      </div>
    );
  };

  // Language settings component - 重构为使用新的i18n系统
  const LanguageSettings = () => (
    <div>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* Interface Language Settings */}
        <Card size="small" title={t('language.title', '语言设置')}>
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <Row gutter={16} align="middle">
              <Col span={6}>
                <Text>{t('language.interface', '界面语言')}:</Text>
              </Col>
              <Col span={18}>
                <LanguageSelector
                  size="middle"
                  showIcon={true}
                  showLabel={false}
                  style={{ width: '100%' }}
                />
              </Col>
            </Row>

            <Row gutter={16} align="middle">
              <Col span={6}>
                <Text>{t('language.current', '当前语言')}:</Text>
              </Col>
              <Col span={18}>
                <Text type="secondary">{currentLanguage}</Text>
              </Col>
            </Row>

            <Divider />
            <Row gutter={16} align="middle">
              <Col span={6}>
                <Text>{t('language.fallback', '回退语言')}:</Text>
              </Col>
              <Col span={18}>
                <Text type="secondary">zh-CN</Text>
              </Col>
            </Row>

            <Row gutter={16} align="middle">
              <Col span={6}>
                <Text>{t('language.supported', '支持的语言')}:</Text>
              </Col>
              <Col span={18}>
                <Text type="secondary">zh-CN, en, ru, fr</Text>
              </Col>
            </Row>
          </Space>
        </Card>

        {/* Format Settings */}
        <Card size="small" title={t('language.formats', '格式设置')}>
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <Row gutter={16} align="middle">
              <Col span={6}>
                <Text>{t('language.dateFormat', '日期格式')}:</Text>
              </Col>
              <Col span={18}>
                <Text type="secondary">YYYY-MM-DD</Text>
              </Col>
            </Row>

            <Row gutter={16} align="middle">
              <Col span={6}>
                <Text>{t('language.timeFormat', '时间格式')}:</Text>
              </Col>
              <Col span={18}>
                <Text type="secondary">HH:mm:ss</Text>
              </Col>
            </Row>

            <Row gutter={16} align="middle">
              <Col span={6}>
                <Text>{t('language.numberFormat', '数字格式')}:</Text>
              </Col>
              <Col span={18}>
                <Text type="secondary">
                  {t('language.decimal', '小数点')}: . | {t('language.thousands', '千分位')}: , | {t('language.currency', '货币')}: ¥
                </Text>
              </Col>
            </Row>
          </Space>
        </Card>
      </Space>
    </div>
  );

  // User Preferences settings component
  const UserPreferencesSettings = () => {
    // Show loading state
    if (userPreferencesLoading || layoutLoading) {
      return (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">{t('settings:messages.loadingSettings', '正在加载设置...')}</Text>
          </div>
        </div>
      );
    }

    // Show error state
    if (userPreferencesError || layoutError) {
      return (
        <Alert
          message={t('settings:messages.loadSettingsFailed', '加载设置失败')}
          description={userPreferencesError ?? layoutError}
          type="error"
          showIcon
        />
      );
    }

    // If no config data, show warning
    if (!userPreferences || !layoutConfig) {
      return (
        <Alert
          message={t('settings:messages.cannotLoadSettings', '无法加载设置')}
          description={t('settings:messages.checkConfigSystem', '请检查配置系统')}
          type="warning"
          showIcon
        />
      );
    }

    return (
      <div>
        <div style={{ marginBottom: 24 }}>
          <Title level={3} style={{ margin: 0 }}>
            {t('settings:userPreferences.title', '用户偏好')}
          </Title>
          <Text type="secondary">
            {t('settings:userPreferences.description', '个性化设置和偏好配置')}
          </Text>
        </div>

        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* Global Preferences */}
          <Card size="small" title={<><UserOutlined /> {t('settings:userPreferences.global.title', '全局偏好')}</>}>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:userPreferences.confirmBeforeDelete', '删除前确认')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={userPreferences.general?.confirmBeforeDelete || false}
                    onChange={(checked) => handleSettingChange('general.confirmBeforeDelete', checked, 'user-preferences')}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:userPreferences.confirmBeforeDeleteDesc', '删除项目前显示确认对话框')}
                  </Text>
                </Col>
              </Row>

              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:userPreferences.confirmBeforeExit', '退出前确认')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={userPreferences.general?.confirmBeforeExit || false}
                    onChange={(checked) => handleSettingChange('general.confirmBeforeExit', checked, 'user-preferences')}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:userPreferences.confirmBeforeExitDesc', '退出应用前显示确认对话框')}
                  </Text>
                </Col>
              </Row>

              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:userPreferences.rememberWindowState', '记住窗口状态')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={userPreferences.general?.rememberWindowState || false}
                    onChange={(checked) => handleSettingChange('general.rememberWindowState', checked, 'user-preferences')}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:userPreferences.rememberWindowStateDesc', '记住窗口大小和位置')}
                  </Text>
                </Col>
              </Row>

              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:userPreferences.enableNotifications', '启用通知')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={userPreferences.general?.enableNotifications || false}
                    onChange={(checked) => handleSettingChange('general.enableNotifications', checked, 'user-preferences')}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:userPreferences.enableNotificationsDesc', '显示系统通知')}
                  </Text>
                </Col>
              </Row>

              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:userPreferences.enableDragDrop', '启用拖拽')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={userPreferences.fileOperations?.enableDragDrop || false}
                    onChange={(checked) => handleSettingChange('fileOperations.enableDragDrop', checked, 'user-preferences')}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:userPreferences.enableDragDropDesc', '支持文件拖拽操作')}
                  </Text>
                </Col>
              </Row>

              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:userPreferences.showFileExtensions', '显示文件扩展名')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={userPreferences.fileOperations?.showFileExtensions || false}
                    onChange={(checked) => handleSettingChange('fileOperations.showFileExtensions', checked, 'user-preferences')}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:userPreferences.showFileExtensionsDesc', '在文件列表中显示扩展名')}
                  </Text>
                </Col>
              </Row>
            </Space>
          </Card>

          {/* File List Preferences */}
          <Card size="small" title={<><FileTextOutlined /> {t('settings:userPreferences.fileList.title', '文件列表偏好')}</>}>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:userPreferences.fileList.showSize', '显示大小')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={layoutConfig.fileList?.showSize !== false}
                    onChange={(checked) => handleSettingChange('fileList.showSize', checked, 'layout-config')}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:userPreferences.fileList.showSizeDesc', '在文件列表中显示文件大小')}
                  </Text>
                </Col>
              </Row>

              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:userPreferences.fileList.showModifiedTime', '显示修改时间')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={layoutConfig.fileList?.showModifiedTime !== false}
                    onChange={(checked) => handleSettingChange('fileList.showModifiedTime', checked, 'layout-config')}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:userPreferences.fileList.showModifiedTimeDesc', '在文件列表中显示最后修改时间')}
                  </Text>
                </Col>
              </Row>

              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:userPreferences.fileList.showAddedTime', '显示添加时间')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={layoutConfig.fileList?.showAddedTime !== false}
                    onChange={(checked) => handleSettingChange('fileList.showAddedTime', checked, 'layout-config')}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:userPreferences.fileList.showAddedTimeDesc', '在文件列表中显示添加到列表的时间')}
                  </Text>
                </Col>
              </Row>

              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('settings:userPreferences.fileList.showLaunchCount', '显示启动次数')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={layoutConfig.fileList?.showLaunchCount !== false}
                    onChange={(checked) => handleSettingChange('fileList.showLaunchCount', checked, 'layout-config')}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {t('settings:userPreferences.fileList.showLaunchCountDesc', '在文件列表中显示文件启动次数')}
                  </Text>
                </Col>
              </Row>
            </Space>
          </Card>
        </Space>
      </div>
    );
  };

  const tabItems = [
    {
      key: 'general',
      label: t('tabs.general', '常规设置'),
      icon: <SettingOutlined />,
      children: <GeneralSettings />,
    },
    {
      key: 'userPreferences',
      label: t('tabs.userPreferences', '用户偏好'),
      icon: <UserOutlined />,
      children: <UserPreferencesSettings />,
    },
    {
      key: 'language',
      label: t('tabs.language', '语言设置'),
      icon: <GlobalOutlined />,
      children: <LanguageSettings />,
    },
    {
      key: 'config',
      label: t('tabs.config', '配置管理'),
      icon: <ToolOutlined />,
      children: <ConfigPanel />,
    },
  ];

  return (
    <div className="settings-panel-container page-container">
      <Card
        className="apple-card"
        style={{
          background: themeConfig.glassEffect
            ? 'var(--glass-background)'
            : 'rgba(255, 255, 255, 0.8)', // 半透明背景，不完全遮挡
          backdropFilter: themeConfig.glassEffect
            ? 'var(--glass-backdrop-filter)'
            : 'blur(10px)',
        }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="small"
        />
      </Card>
    </div>
  );
};

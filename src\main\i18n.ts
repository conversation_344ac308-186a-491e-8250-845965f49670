import i18n from 'i18next';
import { readFileSync, existsSync } from 'fs';
import { join } from 'path';
import type { SupportedLanguage, TranslationNamespace } from '../i18n';

// 主进程i18n类
class MainI18n {
  private i18nInstance: typeof i18n;
  private initialized = false;
  private currentLanguage: SupportedLanguage = 'zh-CN';

  constructor() {
    this.i18nInstance = i18n.createInstance();
  }

  /**
   * 初始化主进程i18n
   */
  async initialize(language?: SupportedLanguage): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // 加载语言资源
      const resources = await this.loadResources();

      await this.i18nInstance.init({
        lng: language ?? 'zh-CN',
        fallbackLng: 'zh-CN',
        resources,
        defaultNS: 'common',
        ns: ['common', 'menu', 'theme', 'settings', 'errors', 'file', 'about', 'config', 'logs'],
        interpolation: {
          escapeValue: false,
        },
        debug: process.env.NODE_ENV === 'development',
      });

      this.currentLanguage = language ?? 'zh-CN';
      this.initialized = true;

      // 使用翻译函数记录初始化成功
      const successMsg = this.t('logs.system.info.main_i18n_initialized', 'MainI18n initialized with language: {{language}}', { language: this.currentLanguage });
      console.log(successMsg);
    } catch (error) {
      // 初始化失败时无法使用翻译函数，使用硬编码英文
      console.error('MainI18n failed to initialize:', error);
      throw error;
    }
  }

  /**
   * 加载语言资源文件
   */
  private async loadResources(): Promise<Record<string, Record<string, Record<string, string>>>> {
    const resources: Record<string, Record<string, Record<string, string>>> = {};
    const supportedLanguages: SupportedLanguage[] = ['zh-CN', 'en', 'ru', 'fr'];
    const namespaces: TranslationNamespace[] = ['common', 'menu', 'theme', 'settings', 'errors', 'file', 'about', 'config', 'logs'];

    for (const lang of supportedLanguages) {
      resources[lang] = {};
      
      for (const ns of namespaces) {
        try {
          const filePath = join(__dirname, '../../src/locales', lang, `${ns}.json`);
          
          if (existsSync(filePath)) {
            const content = readFileSync(filePath, 'utf-8');
            resources[lang][ns] = JSON.parse(content);
          } else {
            // 使用英文作为回退语言记录缺失文件
            console.warn(`[MainI18n] Missing translation file: ${filePath}`);
            resources[lang][ns] = {};
          }
        } catch (error) {
          // 使用英文作为回退语言记录加载失败
          console.error(`[MainI18n] Failed to load ${lang}/${ns}:`, error);
          resources[lang][ns] = {};
        }
      }
    }

    return resources;
  }

  /**
   * 翻译函数
   */
  t(key: string, defaultValue?: string, options?: Record<string, unknown>): string {
    if (!this.initialized) {
      // 未初始化时使用英文警告
      console.warn('MainI18n not initialized, returning default value');
      return defaultValue ?? key;
    }

    try {
      return this.i18nInstance.t(key, defaultValue ?? key, options);
    } catch (error) {
      // 翻译失败时使用英文错误消息
      console.error(`MainI18n translation failed for key: ${key}`, error);
      return defaultValue ?? key;
    }
  }

  /**
   * 切换语言
   */
  async changeLanguage(language: SupportedLanguage): Promise<void> {
    if (!this.initialized) {
      throw new Error('[MainI18n] Not initialized');
    }

    try {
      await this.i18nInstance.changeLanguage(language);
      this.currentLanguage = language;
      console.log(`[MainI18n] Language changed to: ${language}`);
    } catch (error) {
      console.error(`[MainI18n] Failed to change language to ${language}:`, error);
      throw error;
    }
  }

  /**
   * 获取当前语言
   */
  getCurrentLanguage(): SupportedLanguage {
    return this.currentLanguage;
  }

  /**
   * 检查是否已初始化
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 获取支持的语言列表
   */
  getSupportedLanguages(): SupportedLanguage[] {
    return ['zh-CN', 'en', 'ru', 'fr'];
  }

  /**
   * 重新加载资源
   */
  async reload(): Promise<void> {
    if (!this.initialized) {
      return;
    }

    try {
      const resources = await this.loadResources();
      
      // 重新初始化
      await this.i18nInstance.init({
        lng: this.currentLanguage,
        fallbackLng: 'zh-CN',
        resources,
        defaultNS: 'common',
        ns: ['common', 'menu', 'theme', 'settings', 'errors', 'file', 'about', 'config', 'logs'],
        interpolation: {
          escapeValue: false,
        },
        debug: process.env.NODE_ENV === 'development',
      });

      // 使用翻译函数记录重新加载成功
      const successMsg = this.t('logs.system.info.main_i18n_resources_reloaded', 'MainI18n resources reloaded');
      console.log(successMsg);
    } catch (error) {
      // 重新加载失败时使用英文错误消息作为回退
      console.error('MainI18n failed to reload resources:', error);
      throw error;
    }
  }
}

// 创建全局实例
export const mainI18n = new MainI18n();

// 导出类型
export type { SupportedLanguage, TranslationNamespace };

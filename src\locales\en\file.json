{"title": "File Management", "list": {"empty": "File list is empty", "loading": "Loading file list...", "refreshing": "Refreshing...", "total": "Total {{count}} items", "selected": "{{count}} items selected", "filtered": "Showing {{count}} / {{total}} items"}, "actions": {"add": "Add File", "addFile": "Add File", "addFolder": "Add Folder", "remove": "Remove", "delete": "Delete", "open": "Open", "openWith": "Open With", "openFolder": "Open Folder", "showInExplorer": "Show in Explorer", "copyPath": "Copy Path", "rename": "<PERSON><PERSON>", "properties": "Properties", "refresh": "Refresh", "selectAll": "Select All", "clearSelection": "Clear Selection", "clearList": "Clear List", "runAsAdmin": "Run as Administrator", "addToFavorites": "Add to Favorites", "removeFromFavorites": "Remove from Favorites", "pin": "<PERSON>n", "unpin": "Unpin", "duplicate": "Duplicate", "move": "Move", "copy": "Copy", "cut": "Cut", "paste": "Paste"}, "sort": {"title": "Sort Method", "name": "By Name", "date": "By Date", "size": "By Size", "type": "By Type", "frequency": "By Frequency", "recent": "By Recent Use", "ascending": "Ascending", "descending": "Descending"}, "view": {"title": "View Mode", "list": "List View", "grid": "Grid View", "compact": "Compact View", "details": "Details View", "tiles": "Tiles View", "icons": "Icons View"}, "filter": {"title": "Filter", "all": "All", "files": "Files", "folders": "Folders", "favorites": "Favorites", "recent": "Recent", "pinned": "Pinned", "type": "File Type", "size": "File Size", "date": "Modified Date"}, "search": {"placeholder": "Search files...", "noResults": "No matching files found", "searching": "Searching...", "results": "Found {{count}} results", "clear": "Clear Search"}, "properties": {"title": "File Properties", "name": "Name", "path": "Path", "size": "Size", "type": "Type", "created": "Created", "modified": "Modified", "accessed": "Accessed", "permissions": "Permissions", "attributes": "Attributes", "version": "Version", "description": "Description", "company": "Company", "copyright": "Copyright", "launchCount": "Launch Count", "lastLaunched": "Last Launched", "addedDate": "Added Date", "tags": "Tags", "notes": "Notes"}, "status": {"exists": "File Exists", "notExists": "File Not Exists", "accessible": "Accessible", "notAccessible": "Not Accessible", "executable": "Executable", "notExecutable": "Not Executable", "readable": "Readable", "writable": "Writable", "hidden": "Hidden", "system": "System File", "archive": "Archive", "compressed": "Compressed", "encrypted": "Encrypted", "symlink": "Symbolic Link", "shortcut": "Shortcut"}, "types": {"file": "File", "folder": "Folder", "executable": "Executable", "document": "Document", "image": "Image", "video": "Video", "audio": "Audio", "archive": "Archive", "code": "Code File", "data": "Data File", "system": "System File", "unknown": "Unknown Type"}, "messages": {"loadFailed": "Failed to load file list", "addFileSuccess": "File added: {{name}}", "addFileFailed": "Failed to add file", "addFolderSuccess": "Folder added: {{name}}", "addFolderFailed": "Failed to add folder", "launchSuccess": "Launched: {{name}}", "launchFailed": "Failed to launch: {{name}}", "launchError": "Failed to launch file", "removeSuccess": "File removed from list", "removeFailed": "Failed to delete file", "removeError": "Failed to delete file", "dragFilesSuccess": "Added {{count}} files", "dragFilesFailed": "Failed to add files", "dragFilesError": "Failed to process dragged files", "dragFilesWarning": "Cannot get file path, please use add button to select files", "fileAdded": "File added", "filesAdded": "Added {{count}} files", "fileRemoved": "File removed", "filesRemoved": "Removed {{count}} files", "fileOpened": "File opened", "fileNotFound": "File not found", "accessDenied": "Access denied", "operationFailed": "Operation failed", "confirmRemove": "Confirm to remove selected files?", "confirmDelete": "Confirm to delete selected files? This operation cannot be undone!", "confirmClearList": "Confirm to clear file list?", "dragDropHint": "Click or drag files to this area to add to quick launch list", "dragDropSupport": "Support single or batch adding files and folders", "noFileSelected": "No file selected", "invalidPath": "Invalid path", "pathTooLong": "Path too long", "nameRequired": "Please enter file name", "nameInvalid": "Invalid file name", "alreadyExists": "File already exists", "copySuccess": "Path copied to clipboard", "launchTooltip": "Launch", "deleteTooltip": "Delete"}, "ui": {"title": "File List", "description": "Manage your quick launch files and folders", "modifiedTime": "Modified: {{date}}", "fileSize": "Size: {{size}}", "addedTime": "Added: {{date}}", "launchCount": "Launch count: {{count}}", "lastLaunched": "Last launched: {{date}}"}}
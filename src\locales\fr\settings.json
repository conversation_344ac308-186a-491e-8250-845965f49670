{"title": "Paramètres de l'application", "tabs": {"general": "Général", "userPreferences": "Préférences utilisateur", "appearance": "Apparence", "behavior": "Comportement", "advanced": "<PERSON><PERSON><PERSON>", "about": "À propos", "language": "<PERSON><PERSON>", "updates": "<PERSON>ses à jour", "privacy": "Confidentialité", "security": "Sécurité", "performance": "Performance", "config": "Gestion de la configuration"}, "general": {"title": "Paramètres généraux", "language": "Langue de l'interface", "autoStart": "Démarrage automatique", "minimizeToTray": "Réduire dans la barre d'état", "closeToTray": "Fe<PERSON><PERSON> dans la barre d'état", "showSplashScreen": "Afficher l'écran de démarrage", "checkUpdatesOnStart": "Vérifier les mises à jour au démarrage", "enableNotifications": "Activer les notifications", "soundEnabled": "<PERSON><PERSON> le son", "confirmExit": "Confirmer la sortie"}, "appearance": {"title": "Paramètres d'apparence", "theme": "Thème", "colorScheme": "Schéma de couleurs", "fontSize": "Taille de police", "fontFamily": "Famille de polices", "windowOpacity": "Opacité de la fenêtre", "showMenuBar": "Afficher la barre de menu", "showStatusBar": "Afficher la barre d'état", "showToolbar": "Afficher la barre d'outils", "compactMode": "Mode compact", "animationsEnabled": "<PERSON><PERSON> les animations"}, "behavior": {"title": "Paramètres de comportement", "doubleClickAction": "Action du double-clic", "singleClickAction": "Action du simple clic", "middleClickAction": "Action du clic du milieu", "contextMenuEnabled": "<PERSON>r le menu contextuel", "keyboardShortcuts": "<PERSON><PERSON><PERSON><PERSON> clav<PERSON>", "mouseGestures": "<PERSON><PERSON>s de souris", "autoSave": "Sauvegarde automatique", "backupOnExit": "Sauvegarde à la sortie", "confirmActions": "Confirmer les actions", "undoLevels": "Niveaux d'annulation"}, "advanced": {"title": "Paramètres avancés", "debugMode": "Mode débogage", "verboseLogging": "Journalisation détaillée", "experimentalFeatures": "Fonctionnalités expérimentales", "developerMode": "Mode développeur", "customCSS": "CSS personnalisé", "pluginSupport": "Support des plugins", "apiAccess": "Accès API", "advancedSearch": "Recherche avancée", "indexing": "Indexation", "caching": "Mise en cache"}, "language": {"title": "Paramètres de langue", "interface": "Langue de l'interface", "current": "Langue actuelle", "autoDetect": "Détection automatique", "followSystem": "Suivre le système", "availableLanguages": "Langues disponibles", "downloadLanguagePack": "Télécharger le pack de langue", "languagePackStatus": "Statut du pack de langue", "restartRequired": "Redémarrage requis", "autoDetectDesc": "Sélectionner automatiquement la langue de l'interface basée sur la langue du système", "fallback": "Langue de secours", "supported": "Langues supportées", "formats": "Paramètres de format", "dateFormat": "Format de date", "timeFormat": "Format d'heure", "numberFormat": "Format de nombre", "decimal": "Point décimal", "thousands": "Séparateur de milliers", "currency": "<PERSON><PERSON>"}, "updates": {"title": "Paramètres de mise à jour", "autoUpdate": "Mise à jour automatique", "checkForUpdates": "Vérifier les mises à jour", "updateChannel": "Canal de mise à jour", "stable": "Stable", "beta": "<PERSON><PERSON><PERSON>", "dev": "Développement", "downloadInBackground": "Télécharger en arrière-plan", "installOnRestart": "Installer au redémarrage", "currentVersion": "Version actuelle", "latestVersion": "Dernière version", "updateAvailable": "Mise à jour disponible", "upToDate": "À jour"}, "privacy": {"title": "Paramètres de confidentialité", "analytics": "Analyse d'utilisation", "crashReports": "Rapports de plantage", "errorReporting": "Rapport d'erreurs", "usageStatistics": "Statistiques d'utilisation", "dataCollection": "Collecte de don<PERSON>", "anonymousData": "Données anonymes", "clearData": "Efface<PERSON> les données", "dataLocation": "Emplacement des données", "exportData": "Exporter les données", "deleteData": "Supprimer les données"}, "security": {"title": "Paramètres de sécurité", "autoLock": "Verrouillage automatique", "lockTimeout": "<PERSON><PERSON><PERSON>", "requirePassword": "Exiger un mot de passe", "encryptData": "<PERSON><PERSON><PERSON> les données", "secureConnection": "Connexion sécurisée", "certificateValidation": "Validation de certificat", "trustedSources": "Sources de confiance", "blockUnsafeContent": "Bloquer le contenu non sécurisé", "sandboxMode": "Mode bac à sable", "permissionManagement": "Gestion des permissions"}, "performance": {"title": "Optimisation des performances", "hardwareAcceleration": "Accélération matérielle", "hardwareAccelerationDesc": "Activer l'accélération matérielle GPU pour de meilleures performances", "cacheSize": "<PERSON><PERSON> (Mo)", "cacheSizeDesc": "Taille du cache des icônes et miniatures", "enableVirtualization": "Activer la virtualisation", "enableVirtualizationDesc": "Utiliser le défilement virtuel pour un grand nombre de fichiers"}, "actions": {"save": "Enregistrer les paramètres", "cancel": "Annuler", "apply": "Appliquer", "reset": "Réinitialiser", "restore": "<PERSON><PERSON><PERSON>", "export": "Exporter", "import": "Importer", "backup": "<PERSON><PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON><PERSON>", "refresh": "Actualiser"}, "messages": {"settingsSaved": "Paramètres enregistrés", "settingsReset": "Paramètres réinitialisés", "settingsExported": "Paramètres exportés", "settingsImported": "Paramètres importés", "restartRequired": "Redémarrage requis pour appliquer les modifications", "confirmReset": "Confirmer la réinitialisation de tous les paramètres ?", "confirmClearCache": "Confirmer l'effacement du cache ?", "confirmFactoryReset": "Confirmer la réinitialisation d'usine ? Cela supprimera toutes les données !", "invalidSettingsFile": "<PERSON><PERSON>er de paramètres invalide", "settingsCorrupted": "Fichier de paramètres corrompu, utilisation des paramètres par défaut", "settingUpdated": "Paramètre mis à jour", "settingUpdateFailed": "Échec de la mise à jour du paramètre", "settingUpdateError": "Erreur lors de la mise à jour du paramètre", "languageChanged": "<PERSON>ue modifiée", "loadingSettings": "Chargement des paramètres...", "loadSettingsFailed": "Échec du chargement des paramètres", "cannotLoadSettings": "Impossible de charger les paramètres", "checkConfigSystem": "Veuillez vérifier si le système de configuration fonctionne correctement"}, "window": {"title": "Paramètres de fenêtre", "maximizeOnStart": "Maximiser au démarrage", "maximizeOnStartDesc": "Maximiser automatiquement la fenêtre au démarrage de l'application", "alwaysOnTop": "Toujours au premier plan", "alwaysOnTopDesc": "Garder la fenêtre toujours au premier plan"}, "startup": {"title": "Paramètres de démarrage", "autoLaunch": "Lancement automatique", "autoLaunchDesc": "Lancer automatiquement QuickStart au démarrage du système", "minimizeToTray": "Réduire dans la barre d'état", "minimizeToTrayDesc": "Réduire dans la barre d'état système lors de la fermeture de la fenêtre", "showSplashScreen": "Afficher l'écran de démarrage", "showSplashScreenDesc": "Afficher l'écran d'accueil au démarrage de l'application", "checkUpdates": "Vérifier les mises à jour", "checkUpdatesDesc": "Vérifier les mises à jour de l'application au démarrage"}, "userPreferences": {"title": "Préférences utilisateur", "description": "Paramètres de personnalisation et configuration des préférences", "global": {"title": "Préférences globales"}, "fileList": {"title": "Préférences de la liste de fichiers", "showSize": "Afficher la taille", "showSizeDesc": "Afficher la taille du fichier dans la liste des fichiers", "showModifiedTime": "Afficher l'heure de modification", "showModifiedTimeDesc": "Afficher l'heure de dernière modification dans la liste des fichiers", "showAddedTime": "Afficher l'heure d'ajout", "showAddedTimeDesc": "Afficher l'heure d'ajout du fichier à la liste", "showLaunchCount": "Afficher le nombre de lancements", "showLaunchCountDesc": "Afficher le nombre de lancements du fichier dans la liste"}, "confirmBeforeDelete": "Confirmer avant suppression", "confirmBeforeDeleteDesc": "Afficher une boîte de dialogue de confirmation avant de supprimer des fichiers", "confirmBeforeExit": "Confirmer avant sortie", "confirmBeforeExitDesc": "Afficher une boîte de dialogue de confirmation avant de quitter l'application", "rememberWindowState": "Mémoriser l'état de la fenêtre", "rememberWindowStateDesc": "Mémoriser la taille et la position de la fenêtre", "enableNotifications": "Activer les notifications", "enableNotificationsDesc": "Afficher les notifications système", "enableDragDrop": "<PERSON><PERSON> le glisser-dé<PERSON>r", "enableDragDropDesc": "Prendre en charge le glisser-déposer de fichiers vers l'application", "showFileExtensions": "Afficher les extensions de fichier", "showFileExtensionsDesc": "Afficher les extensions dans la liste des fichiers"}}
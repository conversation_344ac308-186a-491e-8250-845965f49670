{"title": "主题设置", "mode": {"light": "浅色模式", "dark": "深色模式", "auto": "跟随系统", "toggle": "切换主题模式"}, "presets": {"title": "预设主题", "iosBlue": "iOS 蓝色", "macosGreen": "macOS 绿色", "appleOrange": "Apple 橙色", "deepSpaceGray": "深空灰", "midnightBlue": "午夜蓝", "custom": "自定义主题"}, "colors": {"title": "界面颜色", "primary": "主色调", "secondary": "辅助色", "background": "背景色", "surface": "表面色", "text": "文字色", "textSecondary": "次要文字色", "border": "边框色", "success": "成功色", "warning": "警告色", "error": "错误色", "info": "信息色", "accent": "强调色", "muted": "静音色", "highlight": "高亮色", "shadow": "阴影色", "interface": "界面颜色", "labels": {"surface": "表面色", "text": "文字色", "textSecondary": "次要文字", "border": "边框色", "shadow": "阴影色"}, "descriptions": {"surface": "用于卡片和面板的背景色", "text": "主要文字颜色", "textSecondary": "次要文字和说明文字颜色", "border": "边框和分割线颜色", "shadow": "阴影和投影颜色"}}, "fonts": {"title": "字体设置", "family": "字体族", "size": "字体大小", "weight": "字体粗细", "lineHeight": "行高", "letterSpacing": "字母间距", "systemFont": "系统字体", "customFont": "自定义字体", "configuration": "字体配置", "options": {"appleSystem": "Apple 系统字体", "microsoftYaHei": "Microsoft YaHei", "pingFangSC": "PingFang SC", "helveticaNeue": "Helvetica Neue", "sfProDisplay": "SF Pro Display", "segoeUI": "Segoe UI"}, "weights": {"thin": "细体", "light": "轻体", "regular": "常规", "medium": "中等", "semibold": "半粗体", "bold": "粗体", "heavy": "重体"}}, "effects": {"title": "视觉效果", "glassEffect": "毛玻璃效果", "transparency": "透明度", "blur": "模糊度", "shadows": "阴影效果", "animations": "动画效果", "transitions": "过渡效果", "borderRadius": "圆角大小", "gradient": "渐变效果", "compactMode": "紧凑模式"}, "layout": {"title": "布局设置", "sidebar": "侧边栏", "sidebarWidth": "侧边栏宽度", "sidebarPosition": "侧边栏位置", "header": "头部", "headerHeight": "头部高度", "footer": "底部", "footerHeight": "底部高度", "spacing": "间距", "padding": "内边距", "margin": "外边距"}, "background": {"title": "背景设置", "type": "背景类型", "color": "纯色背景", "gradient": "渐变背景", "image": "图片背景", "pattern": "图案背景", "opacity": "背景透明度", "position": "背景位置", "size": "大小", "repeat": "背景重复", "attachment": "背景附着", "blend": "混合模式", "enabled": "启用背景", "basicSettings": "基础设置", "colorSettings": "颜色设置", "gradientSettings": "渐变设置", "imageSettings": "图片设置", "reset": "重置设置", "gradientType": "渐变类型", "gradientDirection": "渐变方向", "gradientColors": "渐变颜色", "startColor": "起始", "endColor": "结束", "colorStop": "颜色", "addColor": "添加颜色", "removeColor": "删除颜色", "noGradientColors": "没有渐变颜色", "clickAddColor": "点击\"添加颜色\"按钮添加渐变颜色", "gradientTypes": {"linear": "线性渐变", "radial": "径向渐变"}, "types": {"none": "无背景", "color": "纯色", "gradient": "渐变", "image": "图片"}, "performanceSettings": "性能设置", "performanceTip": "性能提示", "performanceDesc": "高分辨率背景图片可能影响应用性能，建议使用适当的压缩设置。", "localImage": "本地图片", "networkImage": "网络图片", "dragOrClick": "点击或拖拽图片到此区域上传", "supportFormats": "支持 JPG、PNG、GIF、WebP 格式，最大 50MB", "compressionTip": "压缩提示", "compressionDesc": "大尺寸图片将自动压缩以优化性能，同时保持良好的视觉效果。", "displayMode": "显示模式", "blur": "模糊度", "brightness": "亮度", "contrast": "对比度", "saturation": "饱和度", "modes": {"stretch": "拉伸", "tile": "平铺", "center": "居中", "cover": "覆盖", "contain": "包含"}, "processing": "正在处理图片...", "urlLoadSuccess": "网络图片加载成功", "urlLoadFailed": "网络图片加载失败", "removeSuccess": "背景图片已移除", "preview": "预览", "viewFullSize": "查看原图", "removeImage": "移除图片", "loadError": "加载背景配置失败", "noConfig": "背景配置不可用", "configWillBeCreated": "配置将在首次使用时创建", "urlImage": "网络图片", "enterImageUrl": "请输入图片链接 (https://...)", "urlTip": "网络图片提示", "urlDesc": "请确保图片链接可以正常访问，支持 HTTPS 协议的图片链接。", "enterUrl": "请输入图片链接", "uploadSuccess": "背景图片上传成功", "uploadFailed": "背景图片上传失败", "downloadingImage": "正在下载网络图片...", "processingLocalImage": "正在处理本地图片...", "compressingImage": "正在压缩图片以优化性能...", "savingImage": "正在保存背景图片...", "imageSetSuccess": "背景图片设置成功", "imageSetFailed": "背景图片设置失败", "usingMemoryMode": "使用内存模式设置背景（重启后需重新设置）", "imageSetSuccessMemory": "背景图片设置成功（临时模式）", "imageSetFailedComplete": "背景图片设置完全失败", "imageInfo": "图片信息", "dimensions": "尺寸", "format": "格式", "aspectRatio": "宽高比", "imageLoadError": "图片加载失败", "fileInvalidError": "文件对象无效", "emptyFileError": "文件大小为0，可能是空文件", "fileTypeError": "文件类型无效，期望图片类型", "imageTooLargeError": "图片文件过大，请选择小于50MB的图片", "imageTimeoutError": "图片加载超时，文件可能损坏或过大", "defaultImageName": "背景图片", "duplicateFileName": "文件名重复", "duplicateFileContent": "文件名 \"{{fileName}}\" 已存在，是否重命名保存？", "renameAndSave": "重命名保存", "cancelSave": "取消保存", "cachedImages": "已缓存图片", "noCachedImages": "暂无缓存图片", "cachedImagesDesc": "点击图片可快速切换背景，点击删除按钮可移除缓存", "switchToImage": "切换到此背景", "removeFromCache": "从缓存中删除", "confirmRemoveCache": "确认删除缓存", "confirmRemoveCacheContent": "确定要从缓存中删除图片 \"{{fileName}}\" 吗？此操作不可撤销。", "removeCacheSuccess": "已从缓存中删除图片", "removeCacheFailed": "删除缓存图片失败", "imageCache": "图片缓存", "cacheManagement": "缓存管理", "customFileName": "自定义文件名", "enterCustomFileName": "请输入自定义文件名", "fileNameInvalid": "文件名无效", "renameImage": "重命名图片", "newFileName": "新文件名", "renameSuccess": "重命名成功", "renameFailed": "重命名失败", "enterNewFileName": "请输入新的文件名", "imageCorruptedError": "图片尺寸无效，文件可能损坏或不是有效的图片格式", "webpNotSupportedError": "WebP格式在当前浏览器中不受支持，请使用JPG或PNG格式", "imageDamageHints": "可能的原因：文件可能已损坏；图片格式可能不受支持；文件权限可能有问题", "jpegCorruptedError": "JPEG文件存在但可能已损坏，请尝试使用图片编辑软件重新保存", "pngCorruptedError": "PNG文件存在但可能已损坏，请尝试使用图片编辑软件重新保存", "gifCorruptedError": "GIF文件存在但可能已损坏，请尝试使用图片编辑软件重新保存", "webpCorruptedError": "WebP文件存在但可能已损坏或不被当前环境支持", "bmpCorruptedError": "BMP文件存在但可能已损坏，建议转换为JPG或PNG格式", "fileReadTimeoutError": "文件读取超时，文件可能过大或损坏", "imageProcessTimeoutError": "图片处理超时，图片可能过大", "permissionDeniedError": "缓存目录权限不足，请检查应用权限设置", "diskSpaceError": "磁盘空间不足，无法保存背景图片", "cacheNotInitializedError": "缓存系统未初始化，正在重试...", "imageCacheFailedError": "图片缓存失败: {{message}}", "imageValidation": {"unsupportedFormat": "不支持的图片格式: {{format}}。支持的格式: JPEG, PNG, GIF, WebP", "fileTooLarge": "图片文件过大: {{size}}MB。最大支持: 50MB", "fileInvalid": "文件对象无效", "emptyFile": "文件大小为0，可能是空文件", "invalidFileType": "文件类型无效: {{type}}，期望图片类型", "loadTimeout": "图片加载超时（5秒），文件可能损坏或过大", "readTimeout": "文件读取超时（10秒），文件可能过大或损坏", "invalidDimensions": "图片尺寸无效，文件可能损坏或不是有效的图片格式", "cannotReadFile": "无法读取图片文件", "jpegCorrupted": "JPEG文件存在但可能已损坏，请尝试使用图片编辑软件重新保存", "pngCorrupted": "PNG文件存在但可能已损坏，请尝试使用图片编辑软件重新保存", "gifCorrupted": "GIF文件存在但可能已损坏，请尝试使用图片编辑软件重新保存", "webpCorrupted": "WebP文件存在但可能已损坏或不被当前环境支持", "bmpCorrupted": "BMP文件存在但可能已损坏，建议转换为JPG或PNG格式", "webpNotSupported": "WebP格式在当前浏览器中不受支持，请使用JPG或PNG格式", "fileCorruptedGeneric": "文件不是有效的图片格式，或者已损坏。文件头: {{header}}...", "unsupportedFileType": "文件类型不支持: {{type}}。请选择有效的图片文件 (JPG, PNG, GIF, WebP)", "fileTooLargeSimple": "图片文件过大，请选择小于50MB的图片", "corruptionSuggestions": "可能的原因：文件可能已损坏；图片格式可能不受支持；文件权限可能有问题"}, "imageProcessing": {"timeout": "图片处理超时（15秒），图片可能过大", "cannotCreateCanvas": "无法创建Canvas上下文", "cannotGeneratePreview": "无法生成图片预览", "processingFailed": "图片处理失败: {{message}}", "cannotLoadForProcessing": "无法加载图片进行处理", "batchProcessingFailed": "处理图片 {{fileName}} 失败", "setImageSourceFailed": "设置图片源失败: {{message}}", "fileReadFailed": "文件读取失败: {{message}}", "unknownError": "未知错误"}, "imageNetwork": {"downloadTimeout": "网络图片下载超时：{{seconds}}秒", "loadTimeout": "网络图片加载超时：{{seconds}}秒", "corsError": "CORS错误：无法访问此图片，服务器不允许跨域请求", "networkError": "网络错误：请检查网络连接和图片链接是否有效", "downloadFailed": "下载网络图片失败: {{message}}", "httpError": "HTTP {{status}}: {{statusText}}", "invalidContentType": "无效的内容类型: {{contentType}}。期望的是图片类型。", "invalidUrl": "无效的图片链接格式", "protocolNotSupported": "只支持HTTP和HTTPS协议的图片链接", "invalidExtension": "图片链接必须以支持的格式结尾 (.jpg, .jpeg, .png, .gif, .webp)", "downloadTimeoutRetry": "网络图片下载超时，请检查网络连接或稍后重试", "corsAccessDenied": "无法访问此图片，服务器不允许跨域请求", "networkConnectionError": "网络连接错误，请检查网络设置", "serverError": "服务器错误: {{message}}", "fileTooLargeNetwork": "网络图片文件过大", "invalidImageLink": "链接不是有效的图片文件", "genericError": "{{baseMessage}}: {{details}}"}, "cache": {"initializationFailed": "缓存管理器初始化失败: {{message}}", "permissionDeniedCreate": "权限被拒绝：无法创建或写入缓存目录: {{dir}}。请检查文件夹权限。", "insufficientDiskSpaceCreate": "磁盘空间不足，无法创建缓存目录: {{dir}}", "invalidPathCreate": "路径无效：文件存在于应创建目录的位置: {{dir}}", "ensureCacheDirectoryFailed": "确保缓存目录失败: {{message}}", "insufficientDiskSpaceCache": "磁盘空间不足，需要 {{required}}MB，可用 {{available}}MB", "fileWriteVerificationFailed": "文件写入验证失败：期望 {{expected}} 字节，得到 {{actual}} 字节", "insufficientDiskSpace": "磁盘空间不足，无法缓存图片", "permissionDeniedWrite": "权限被拒绝：无法写入缓存目录: {{dir}}", "tooManyOpenFiles": "打开文件过多：无法缓存图片", "cacheImageFailed": "缓存图片失败: {{message}}", "cleanupPartialFileFailed": "清理部分文件失败"}}, "actions": {"apply": "应用主题", "reset": "重置主题", "customize": "自定义主题", "duplicate": "复制主题", "rename": "重命名主题", "share": "分享主题"}, "messages": {"themeApplied": "主题已应用", "themeReset": "主题已重置", "themeSaved": "主题已保存", "themeLoaded": "主题已加载", "themeExported": "主题配置已导出", "themeImported": "主题配置已导入", "themeImportError": "主题配置文件格式错误", "layoutUpdated": "布局设置已更新", "layoutUpdateFailed": "布局设置更新失败", "layoutUpdateError": "布局设置更新时发生错误", "loadingLayout": "正在加载布局设置...", "loadLayoutFailed": "加载布局设置失败", "noLayoutConfig": "无法加载布局设置", "checkConfigSystem": "请检查配置系统是否正常工作", "invalidThemeFile": "无效的主题文件", "themeNameRequired": "请输入主题名称", "themeNameExists": "主题名称已存在", "confirmDeleteTheme": "确认删除此主题？", "confirmResetTheme": "确认重置主题设置？"}, "global": {"title": "全局主题", "primaryColor": "主色调", "primaryColorDesc": "影响按钮、链接等主要元素的颜色", "mode": "显示模式", "darkMode": "暗黑模式", "modeDesc": "切换明亮和暗黑显示模式", "font": "字体设置", "fontSize": "字体大小", "borderRadius": "圆角大小"}, "ui": {"title": "样式设置", "description": "自定义应用的外观和主题", "interfaceColors": "界面颜色", "themePresets": "主题预设", "typography": "字体排版", "visualEffects": "视觉效果", "layoutSettings": "布局设置", "primaryColorDesc": "用于按钮、链接等主要元素", "colorLabels": {"background": "背景色", "surface": "表面色", "text": "文字色", "textSecondary": "次要文字", "border": "边框色", "shadow": "阴影色"}, "fontSize": "字体大小", "interfaceEffects": "界面效果", "sidebar": {"title": "侧边栏设置", "position": "侧边栏位置", "width": "侧边栏宽度", "collapsed": "默认折叠", "autoHide": "自动隐藏", "positions": {"left": "左侧", "right": "右侧"}}, "fileList": {"title": "文件列表设置", "showSize": "显示大小", "showModifiedTime": "显示修改时间", "showAddedTime": "显示添加时间", "showLaunchCount": "显示启动次数"}, "statusBar": {"title": "状态栏设置", "visible": "显示状态栏", "showFileCount": "显示文件数量", "showPath": "显示路径"}}}
---
description: 
globs: docs/*.md
alwaysApply: false
---
# 错误追踪管理规范

## 使用场景
- 发现任何代码错误、问题或bug时
- 修复现有错误时
- 进行错误状态管理时
- 项目错误统计和优先级管理时

## 关键规则
- 所有错误统一记录在`docs/error-tracking.md`文件中（英文文件名，中文内容）
- 每个模块文档只保留错误状态概览，详细信息指向统一文档
- 错误必须按照严重程度分类：🔴严重、🟡中等、🟢轻微
- 使用标准错误编号：ERR-{模块}-{日期}-{序号}
- 修复完成时必须从错误追踪文档移除，但在模块文档保留简短记录防复发
- 错误追踪文档只保留未解决的错误，已解决的错误直接移除

## 文件命名规范
- **错误追踪文档**: `docs/error-tracking.md`（英文文件名便于程序调用）
- **文档内容**: 使用中文（便于团队阅读和维护）
- **模块文档**: 中文内容，英文链接

## 错误编号规范
- **格式**: ERR-{模块}-{YYYYMMDD}-{序号}
- **模块代码**: 
  - DB (数据库)
  - FRONTEND (前端)
  - BACKEND (后端)
  - DEPLOY (部署)
  - SECURITY (安全)
  - PERF (性能)
  - I18N (国际化)
  - API (接口)

## 严重程度定义
- **🔴 严重**: 影响核心功能或安全性，需立即修复
- **🟡 中等**: 影响用户体验或稳定性，需要修复
- **🟢 轻微**: 性能优化或代码质量，可延后修复

## 错误管理流程
1. **发现错误** → 立即记录到`docs/error-tracking.md`
2. **错误分析** → 评估严重程度和影响范围
3. **优先级分配** → 根据紧急度排序
4. **修复实施** → 分配开发资源
5. **测试验证** → 确保修复有效
6. **文档更新** → 从错误追踪文档中完全删除已修复错误，在模块文档中添加简短记录

## 文档更新要求（简洁原则）
- **错误追踪文档**: 只保留未解决的错误，已修复错误直接删除
- **模块文档**: 在"已修复错误记录"部分添加简短记录防止复发
- **保持简洁**: 错误追踪文档专注当前问题，不保留历史记录
- **防复发机制**: 模块文档中的简短记录包含关键修复要点

## 示例

<example>
### 错误追踪文档格式（中文内容）
```markdown
### ERR-DB-20250607-01: updateGeneralSetting中存在硬编码SQL
- **文件**: `src/main/database.ts`
- **模块**: 数据库
- **时间**: 2025-06-07
- **严重程度**: 🔴 严重
- **状态**: 🔄 待修复
- **问题描述**: 使用switch语句拼接SQL字符串，代码重复且难维护
- **影响范围**: 代码可维护性
- **修复方案**: 重构为动态SQL生成
- **预计工时**: 4小时
```

### 模块文档错误追踪部分（中文格式）
```markdown
## 🚨 错误追踪

> **重要**: 所有活跃错误已迁移至 [error-tracking.md](mdc:error-tracking.md) 统一管理
> 
> **数据库相关错误状态**: 
> - ERR-DB-20250606-03 🔄 待修复 (数据库连接状态不一致)
> - ERR-DB-20250607-01 🔄 待修复 (硬编码SQL问题)
> 
> **已修复错误记录**（防止复发参考）:
> - ERR-DB-20250606-01 ✅ 已修复 (数据库错误处理不完善) - 添加了ensureDatabaseConnection()方法
> - ERR-DB-20250606-02 ✅ 已修复 (SQL注入潜在风险) - 添加了白名单验证
> 
> **详细信息**: [docs/error-tracking.md](mdc:error-tracking.md)
```
</example>

<example type="invalid">
### 错误的做法
```markdown
# 在模块文档中保留详细的错误信息
### ERR-DB-20250607-01: 详细问题描述...
- 完整的错误信息...
```

### 在错误追踪文档中保留已修复的错误
```markdown
# 错误追踪文档不应包含已修复错误
### ERR-DB-20250606-01: 数据库错误处理不完善 ✅
- 状态: ✅ 已修复
```

### 使用中文文件名
```markdown
# 错误：使用中文文件名
docs/错误追踪.md  # 不便于程序调用和版本控制
```
</example>

## 防复发机制
### 已修复错误记录格式
- **简短描述**: 一句话说明问题
- **修复状态**: ✅ 已修复
- **修复要点**: 关键修复措施，防止类似问题
- **参考价值**: 帮助团队避免重复错误

### 记录位置
- **错误追踪文档**: 不保留已修复错误
- **模块文档**: 在"已修复错误记录"部分保留简短记录
- **目的**: 维护简洁的错误追踪文档，同时保留历史经验

## 优先级建议
### 第一优先级 (立即处理)
- 🔴 严重安全漏洞
- 🔴 系统崩溃或无法启动
- 🔴 数据丢失风险

### 第二优先级 (本周内)
- 🔴 核心功能异常
- 🟡 用户体验严重影响

### 第三优先级 (本月内)
- 🟡 功能缺陷
- 🟡 性能问题

### 第四优先级 (可延后)
- 🟢 代码质量改进
- 🟢 性能优化
- 🟢 文档完善

## 技术实现要点
### 文件管理
- **内容语言**: 中文（团队可读性）
- **链接管理**: 模块文档统一链接到error-tracking.md
- **版本控制**: 英文文件名便于Git管理

### 自动化建议
- 使用脚本检查错误编号重复
- 定期统计错误修复进度
- 自动检查模块文档链接完整性
- 错误优先级自动提醒机制




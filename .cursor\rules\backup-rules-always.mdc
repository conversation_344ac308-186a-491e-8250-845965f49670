---
description: 
globs: 
alwaysApply: true
---
# 备份安全规范

## 使用场景
- 修改任何现有代码文件前
- 生成新代码覆盖旧文件前
- 重要文件操作前

## 关键规则
- 修改文件前必须先备份到copy/目录
- 备份命名规范：首次为原文件名，后续加递增数字
- 自动清理：保留最近5个备份文件
- 必须告知用户备份文件路径
- 使用现代PowerShell命令格式执行备份操作

## 备份流程
1. **检查copy/目录** → 2. **生成备份文件名** → 3. **执行备份** → 4. **确认完成** → 5. **自动清理**

## 推荐命令格式

### 基本备份操作
```powershell
# 第一步：创建copy目录
powershell -Command "New-Item -Path 'copy' -ItemType Directory -Force"

# 第二步：执行文件备份
powershell -Command "Copy-Item -Path 'src\app.js' -Destination 'copy\app.js'"

# 第三步：验证备份成功
powershell -Command "if (Test-Path 'copy\app.js') { Write-Host '✅ 备份完成: copy\app.js' -ForegroundColor Green }"
```

### 版本化备份（避免覆盖）
```powershell
# 检查现有备份并生成版本号
powershell -Command "$count = (Get-ChildItem 'copy\app*.js' -ErrorAction SilentlyContinue).Count; $name = if ($count -eq 0) { 'app.js' } else { 'app' + $count + '.js' }; Copy-Item 'src\app.js' ([System.IO.Path]::Combine('copy', $name)); Write-Host ('✅ 备份至: copy\' + $name) -ForegroundColor Green"
```

### 批量备份清理
```powershell
# 清理旧备份，保留最新5个
powershell -Command "$files = Get-ChildItem 'copy\*.js' | Sort-Object CreationTime -Descending | Select-Object -Skip 5; if ($files) { $files | Remove-Item -Force; Write-Host ('🧹 清理了 ' + $files.Count + ' 个旧备份') -ForegroundColor Yellow }"
```

## 示例

### ✅ 正确的现代化备份操作
```powershell
# 单步备份操作
powershell -Command "Copy-Item -Path 'src\components\App.tsx' -Destination 'copy\App_backup.tsx'"

# 条件备份
powershell -Command "if (Test-Path 'src\utils\helper.js') { Copy-Item 'src\utils\helper.js' 'copy\helper_backup.js' }"

# 目录创建和备份
powershell -Command "New-Item -Path 'copy' -ItemType Directory -Force"
powershell -Command "Copy-Item -Path 'config\app.json' -Destination 'copy\app_config.json'"

# 使用时间戳的备份
powershell -Command "Copy-Item 'package.json' ('copy\package_' + (Get-Date -Format 'yyyyMMdd_HHmmss') + '.json')"
```

### ❌ 避免的错误用法
```powershell
# 错误：路径没有单引号保护
powershell -Command "Copy-Item src\app.js copy\backup.js"

# 错误：混用正斜杠和反斜杠
powershell -Command "Copy-Item 'src/app.js' 'copy/backup.js'"

# 错误：命令过长，容易触发缓冲区错误
powershell -Command "if (-not (Test-Path copy)) { New-Item -ItemType Directory -Path copy } ; $count = (Get-ChildItem copy\app*.js).Count ; Copy-Item src\app.js (copy\app + $count + .js) ; Write-Host 备份完成"

# 错误：使用过时的字符串拼接而非Path.Combine
powershell -Command "Copy-Item 'src\app.js' ('copy\' + 'app.js')"
```

## 🔧 故障排查

### 常见问题及解决方案
1. **路径分隔符错误**：
   - 症状：找不到文件或目录
   - 解决：Windows环境统一使用反斜杠 `\`

2. **缓冲区错误**：
   - 症状：`System.ArgumentOutOfRangeException`
   - 解决：使用简洁的单步命令，避免复杂内联操作

3. **路径解析失败**：
   - 症状：Access denied或路径无效
   - 解决：确保所有路径都用单引号包裹

### 最佳实践
- **路径规范**：Windows环境使用反斜杠 `'src\file.js'`
- **现代语法**：使用 `-Force` 参数避免确认提示
- **错误处理**：添加 `-ErrorAction SilentlyContinue`
- **路径安全**：使用 `[System.IO.Path]::Combine()` 方法
- **视觉反馈**：添加颜色输出提升用户体验

## 💡 现代化优势

1. **路径安全**：
   - 使用Windows标准反斜杠路径
   - Path.Combine方法避免路径拼接错误

2. **错误处理**：
   - 现代化的错误处理机制
   - 静默处理和明确的错误反馈

3. **用户体验**：
   - 彩色输出提升可读性
   - 清晰的操作状态反馈
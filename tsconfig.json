{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx", "declaration": true, "outDir": "./build", "rootDir": "./src", "baseUrl": "./src", "paths": {"@/*": ["*"], "@main/*": ["main/*"], "@renderer/*": ["renderer/*"], "@preload/*": ["preload/*"], "@shared/*": ["shared/*"]}, "types": ["node", "jest"]}, "include": ["src/**/*", "*.d.ts"], "exclude": ["node_modules", "build", "dist"], "ts-node": {"compilerOptions": {"module": "CommonJS"}}}
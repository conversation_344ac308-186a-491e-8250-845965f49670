{"title": "О QuickStart", "app": {"name": "QuickStart", "description": "Мощный инструмент быстрого запуска файлов", "version": "v1.0.0-beta"}, "project": {"title": "Обзор проекта", "description1": "QuickStart - это современное настольное приложение, разработанное на основе технологий Electron и React, предназначенное для предоставления пользователям быстрого и удобного запуска файлов и приложений.", "description2": "Использует архитектуру TypeScript + React + Electron, интегрирует UI-фреймворк Ant Design и язык дизайна в стиле Apple, обеспечивая пользователям элегантный и эффективный пользовательский опыт.", "architecture": {"javascript": "TypeScript + React + Electron", "ui": "UI-фреймворк Ant Design", "design": "Язык дизайна в стиле Apple"}}, "techStack": {"title": "Технологический стек", "electron": "Electron 27+", "react": "React 18", "typescript": "TypeScript", "antd": "Ant Design 5", "i18next": "i18next", "webpack": "Webpack 5", "nodejs": "Node.js", "css3": "CSS3"}, "features": {"title": "Основные функции", "styling": {"title": "🎨 Мощная система стилей", "description": "Поддержка переключения тем, настраиваемых цветов, конфигурации шрифтов"}, "i18n": {"title": "🌍 Поддержка интернационализации", "description": "Поддержка китайского, английского, русского, французского языков"}, "design": {"title": "🍎 Дизайн в стиле Apple", "description": "Эффекты матового стекла, элегантные анимации, современный интерфейс"}, "performance": {"title": "⚡ Высокопроизводительная архитектура", "description": "Виртуальная прокрутка, ленивая загрузка, ускорение GPU"}}, "system": {"title": "Системная информация", "electron": "Версия Electron", "node": "Версия Node.js", "chrome": "Версия Chrome"}, "actions": {"title": "Действия", "quick_actions": "Быстрые действия", "version_info": "Информация о версии и действия", "open_source": "Открытый исходный код", "github": "Просмотр исходного кода", "feedback": "Сообщить о проблеме", "support": "Поддержать проект"}, "copyright": {"title": "Информация об авторских правах", "text": "© 2024 Команда QuickStart. Все права защищены.", "license": "Открытый исходный код под лицензией MIT", "disclaimer": "Это программное обеспечение предоставляется \\\"как есть\\\", без каких-либо гарантий, явных или подразумеваемых."}, "tabs": {"overview": "Обзор", "acknowledgments": "Благодарности", "copyright": "Авторские права", "support": "Поддержка"}, "version": {"title": "Информация о версии", "app": "Версия приложения", "build_date": "Дата сборки", "build_date_value": "2025-07-05", "build_number": "Номер сборки", "build_number_value": "Build **********", "environment": "Среда", "environment_value": "Прода<PERSON><PERSON>н"}, "acknowledgments": {"title": "Благодарности", "description": "Благодарим следующие проекты с открытым исходным кодом и технологический стек", "tech_stack": {"electron": {"name": "Electron", "description": "Фреймворк для разработки кроссплатформенных настольных приложений", "url": "https://www.electronjs.org/"}, "react": {"name": "React", "description": "JavaScript библиотека для создания пользовательских интерфейсов", "url": "https://reactjs.org/"}, "typescript": {"name": "TypeScript", "description": "Надмножество JavaScript со статической типизацией", "url": "https://www.typescriptlang.org/"}, "antd": {"name": "Ant Design", "description": "Корпоративный язык дизайна UI и React компоненты", "url": "https://ant.design/"}, "i18next": {"name": "i18next", "description": "Фреймворк интернационализации", "url": "https://www.i18next.com/"}, "webpack": {"name": "Webpack", "description": "Сборщик модулей", "url": "https://webpack.js.org/"}, "nodejs": {"name": "Node.js", "description": "Среда выполнения JavaScript", "url": "https://nodejs.org/"}, "sqlite": {"name": "Better SQLite3", "description": "Высокопроизводительный драйвер базы данных SQLite", "url": "https://github.com/WiseLibs/better-sqlite3"}, "jest": {"name": "Jest", "description": "Фреймворк тестирования JavaScript", "url": "https://jestjs.io/"}, "eslint": {"name": "ESLint", "description": "Инструмент проверки кода JavaScript", "url": "https://eslint.org/"}, "prettier": {"name": "<PERSON>ttier", "description": "Форматировщик кода", "url": "https://prettier.io/"}}}, "support": {"title": "Поддержать проект", "description": "Если этот проект оказался полезным для вас, пожалуйста, рассмотрите возможность поддержки нашей работы."}}
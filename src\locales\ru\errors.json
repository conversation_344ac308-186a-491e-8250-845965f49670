{"general": {"unknown": "Неизвестная ошибка", "networkError": "Ошибка сети", "serverError": "Ошибка сервера", "clientError": "Ошибка клиента", "timeout": "Тайм-аут запроса", "cancelled": "Операция отменена", "forbidden": "Доступ запрещен", "unauthorized": "Неавторизованный доступ", "notFound": "Ресурс не найден", "conflict": "Конфликт ресурсов", "tooManyRequests": "Слишком много запросов", "internalError": "Внутренняя ошибка", "serviceUnavailable": "Сервис недоступен", "badGateway": "Пло<PERSON><PERSON>й шлюз", "gatewayTimeout": "Тайм-аут шлюза"}, "file": {"notFound": "Файл не найден", "accessDenied": "Доступ к файлу запрещен", "alreadyExists": "Файл уже существует", "tooLarge": "Файл слишком большой", "invalidFormat": "Неверный формат файла", "corrupted": "Файл поврежден", "readError": "Ошибка чтения файла", "writeError": "Ошибка записи файла", "deleteError": "Ошибка удаления файла", "copyError": "Ошибка копирования файла", "moveError": "Ошибка перемещения файла", "renameError": "Ошибка переименования файла", "permissionDenied": "Недостаточно прав доступа к файлу", "diskFull": "Диск заполнен", "pathTooLong": "Путь к файлу слишком длинный", "invalidPath": "Неверный путь к файлу", "locked": "Файл заблокирован", "inUse": "Файл используется"}, "config": {"loadFailed": "Ошибка загрузки конфигурации", "saveFailed": "Ошибка сохранения конфигурации", "parseFailed": "Ошибка разбора конфигурации", "validationFailed": "Ошибка проверки конфигурации", "corrupted": "Файл конфигурации поврежден", "notFound": "Файл конфигурации не найден", "accessDenied": "Доступ к файлу конфигурации запрещен", "backupFailed": "Ошибка резервного копирования конфигурации", "restoreFailed": "Ошибка восстановления конфигурации", "migrationFailed": "Ошибка миграции конфигурации", "versionMismatch": "Несоответствие версии конфигурации", "schemaError": "Ошибка схемы конфигурации", "defaultsError": "Ошибка конфигурации по умолчанию"}, "theme": {"loadFailed": "Ошибка загрузки темы", "saveFailed": "Ошибка сохранения темы", "parseFailed": "Ошибка разбора темы", "invalidFormat": "Неверный формат темы", "notFound": "Тема не найдена", "alreadyExists": "Тема уже существует", "exportFailed": "Ошибка экспорта темы", "importFailed": "Ошибка импорта темы", "applyFailed": "Ошибка применения темы", "resetFailed": "Ошибка сброса темы", "colorInvalid": "Неверное значение цвета", "fontNotFound": "Шрифт не найден", "effectNotSupported": "Эффект не поддерживается"}, "app": {"initFailed": "Ошибка инициализации приложения", "startupError": "Ошибка запуска", "shutdownError": "Ошибка завершения", "updateFailed": "Ошибка обновления", "installFailed": "Ошибка установки", "uninstallFailed": "Ошибка удаления", "migrationFailed": "Ошибка миграции данных", "backupFailed": "Ошибка резервного копирования", "restoreFailed": "Ошибка восстановления", "syncFailed": "Ошибка синхронизации", "authFailed": "Ошибка аутентификации", "licenseInvalid": "Неверная лицензия", "versionIncompatible": "Несовместимая версия"}, "ipc": {"channelNotFound": "IPC канал не найден", "handlerNotFound": "IPC обработчик не найден", "messageInvalid": "Неверное IPC сообщение", "timeoutError": "Ошибка тайм-аута IPC", "connectionLost": "Потеряно IPC соединение", "permissionDenied": "IPC доступ запрещен", "serializationError": "Ошибка сериализации IPC", "deserializationError": "Ошибка десериализации IPC", "protocolError": "Ошибка протокола IPC", "bufferOverflow": "Переполнение буфера IPC"}, "database": {"connectionFailed": "Ошибка подключения к базе данных", "queryFailed": "Ошибка запроса к базе данных", "transactionFailed": "Ошибка транзакции базы данных", "migrationFailed": "Ошибка миграции базы данных", "backupFailed": "Ошибка резервного копирования базы данных", "restoreFailed": "Ошибка восстановления базы данных", "corruptedData": "Поврежденные данные базы данных", "schemaError": "Ошибка схемы базы данных", "constraintViolation": "Нарушение ограничений базы данных", "deadlock": "Взаимоблокировка базы данных", "timeout": "Тайм-аут базы данных", "diskFull": "Диск базы данных заполнен", "permissionDenied": "Доступ к базе данных запрещен", "versionMismatch": "Несоответствие версии базы данных"}, "network": {"connectionFailed": "Ошибка сетевого подключения", "requestFailed": "Ошибка сетевого запроса", "responseInvalid": "Неверный сетевой ответ", "timeout": "Тайм-аут сети", "offline": "Сеть отключена", "dnsError": "Ошибка разрешения DNS", "sslError": "Ошибка SSL/TLS", "proxyError": "Ошибка прокси", "rateLimited": "Ограничение скорости", "hostUnreachable": "Хост недоступен", "portBlocked": "Порт заблокирован", "protocolError": "Ошибка протокола"}, "ui": {"renderFailed": "Ошибка отрисовки UI", "componentError": "Ошибка компонента", "eventHandlerError": "Ошибка обработчика событий", "stateUpdateError": "Ошибка обновления состояния", "routingError": "Ошибка маршрутизации", "validationError": "Ошибка валидации формы", "loadingError": "Ошибка загрузки", "resourceNotFound": "UI ресурс не найден"}, "actions": {"retry": "Повторить", "contact": "Связаться с поддержкой", "reload": "Перезагрузить", "restart": "Перезапустить приложение", "reset": "Сбросить", "restore": "Восстановить"}}
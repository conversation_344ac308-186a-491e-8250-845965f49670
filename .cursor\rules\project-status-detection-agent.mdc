---
description: 在新会话或重连时自动检测项目状态，分析模块文档完整性和功能状态，提供合适的下一步建议
globs: 
alwaysApply: false
---
# 项目状态检测规范

## 使用场景
- 新会话开始时
- 用户重新连接项目时
- 需要了解项目当前状态时

## 关键规则
- 自动检测项目类型和技术栈
- 分析docs/模块化文件夹结构的完整性
- 检查各模块目录下文档的状态和错误记录
- 验证模块文档的关联性和导航完整性
- 提供项目概览和下一步建议
- 识别潜在问题和改进点

## 检测流程
1. **项目识别** → 2. **文档扫描** → 3. **状态分析** → 4. **问题识别** → 5. **建议生成**

## 状态分析项目
- 项目类型和技术栈
- 模块文档完整性
- 功能开发状态
- 错误和待修复问题
- 文档同步状态

## 示例

<example>
```text
📊 项目状态检测报告

🔍 项目信息：
- 名称：QuickStart 快速启动工具
- 技术栈：React + TypeScript
- 类型：桌面应用程序

📋 模块状态：
- ✅ 前端模块 (docs/frontend/)：正常 (无错误)
- 🔄 后端模块 (docs/backend/)：开发中 (1个警告)
- ✅ API模块 (docs/api/)：正常 (无错误)
- 🔴 数据库模块 (docs/database/)：异常 (2个错误待修复)
- ✅ 国际化模块 (docs/i18n/)：正常 (无错误)
- ✅ 日志模块 (docs/logging/)：正常 (无错误)
- ✅ 部署模块 (docs/deployment/)：正常 (无错误)

💡 建议下一步：
1. 优先修复数据库模块的2个错误
2. 完善后端API的错误处理
3. 考虑添加单元测试

准备好协助您开发！有什么需要帮助的吗？
```
</example>

<example type="invalid">
仅显示"项目检测完成"而不提供具体状态信息

</example>
---
description: 
globs: 
alwaysApply: true
---
# Windows平台开发规范

## 使用场景
- 所有代码开发和API选择
- 文件路径处理
- 系统功能调用

## 关键规则
- 仅考虑Windows平台兼容性
- 优先使用Windows原生API和.NET Framework
- 路径使用反斜杠格式，支持大小写不敏感
- 禁止使用跨平台专用库和Unix格式
- 利用Windows特性：注册表、服务、COM组件

## 技术选择
- **文件操作**: System.IO命名空间、Node.js fs模块
- **进程管理**: System.Diagnostics.Process、Node.js child_process
- **注册表**: Microsoft.Win32.Registry、Electron原生模块
- **网络**: System.Net命名空间、Node.js网络API
- **桌面应用**: Electron + Windows原生集成

## 示例

<example>
```csharp
// 正确：Windows原生API (.NET应用)
using System.IO;
using Microsoft.Win32;

string path = @"C:\Users\<USER>\Documents\app.txt";
string configPath = Path.Combine(
    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
    "MyApp", "config.json"
);

RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\MyApp");
```

```typescript
// 正确：Electron应用中的Windows集成
import { app } from 'electron';
import * as path from 'path';
import * as fs from 'fs';

// Windows应用数据路径
const appDataPath = path.join(app.getPath('appData'), 'MyApp');
const configPath = path.join(appDataPath, 'config.json');

// Windows路径处理
const documentPath = path.join(app.getPath('documents'), 'app.txt');
```
</example>

<example type="invalid">
```javascript
// 错误：跨平台路径和条件判断
const path = "/home/<USER>/documents/app.txt";
if (os.platform() === 'win32') {
    // 不应该考虑其他平台
}

// 错误：在Electron中不必要的平台检查
if (process.platform === 'win32') {
    // 既然只考虑Windows，就不需要条件判断
}
```

</example>
---
description: 项目中处理时间相关功能时使用，规范时间格式和获取方法
globs: 
alwaysApply: false
---
# 时间功能使用规范

## 使用场景
- 项目中需要处理时间相关功能时
- 在文档中记录时间戳时
- 获取当前时间用于日志、错误记录等

## 关键规则
- 使用 PowerShell 的 `Get-Date` 命令获取时间，默认获取北京时间
- 文档时间记录默认只写年月日（yyyy-MM-dd格式）
- 具体时间使用24小时制格式
- 除非明确要求，否则不需要记录小时分钟秒
- 错误记录时间统一使用 "yyyy-MM-dd" 格式

## 常用时间命令
- 完整时间：`Get-Date -Format "yyyy-MM-dd HH:mm:ss"`
- 仅时间：`Get-Date -Format "HH:mm"`
- 仅日期：`Get-Date -Format "yyyy-MM-dd"`

## 示例

<example>
```powershell
# 正确的时间获取和格式化
$currentDate = Get-Date -Format "yyyy-MM-dd"
$currentTime = Get-Date -Format "HH:mm"
$fullDateTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

# 文档中记录错误时间
Write-Host "错误记录时间: $currentDate"
```
</example>

<example type="invalid">
```javascript
// 错误：使用复杂的时间格式
const now = new Date().toISOString(); // 过于复杂
const time = "2025-06-01 14:30:25.123Z"; // 不需要毫秒和时区
```
</example>

## 文档时间格式

<example>
```markdown
### 🔴 错误：ERR-AUTH-20250602-01
**时间**: 2025-06-02
**描述**: 登录接口返回500错误
```
</example>


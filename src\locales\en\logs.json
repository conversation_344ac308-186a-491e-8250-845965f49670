{"app": {"startup": {"success": "Application started successfully, version: {{version}}", "failed": "Application startup failed: {{error}}", "ready": "App is ready", "initializing": "Initializing application...", "shutdown": "Application is shutting down", "crashed": "Application crashed: {{error}}"}, "environment": {"header": "=== Environment Configuration ===", "footer": "===================", "node_env": "NODE_ENV: {{env}}", "is_packaged": "app.isPackaged: {{packaged}}", "is_dev": "isDev (computed): {{dev}}", "platform": "process.platform: {{platform}}", "arch": "process.arch: {{arch}}", "renderer_url": "RENDERER_URL: {{url}}", "dirname": "__dirname: {{dirname}}"}, "window": {"created": "Window created successfully: {{windowType}}", "failed": "Window creation failed: {{error}}", "closed": "Main window closed", "minimized": "Window minimized", "maximized": "Window maximized", "restored": "Window restored", "creating": "Creating main window...", "loading_url": "Loading renderer URL: {{url}}", "shown": "Main window shown", "renderer_loaded": "Renderer process finished loading"}, "tray": {"created": "System tray created successfully", "failed": "System tray creation failed: {{error}}", "clicked": "System tray clicked"}, "database": {"initialized": "DatabaseManager initialized"}}, "config": {"load": {"success": "Configuration loaded successfully: {{configType}}", "failed": "Configuration load failed: {{configType}}, error: {{error}}", "notFound": "Configuration file not found: {{configType}}, using defaults", "corrupted": "Configuration file corrupted: {{configType}}, reset to defaults"}, "save": {"success": "Configuration saved successfully: {{configType}}", "failed": "Configuration save failed: {{configType}}, error: {{error}}"}, "backup": {"created": "Configuration backup created successfully: {{filename}}", "failed": "Configuration backup creation failed: {{error}}", "restored": "Configuration restored from backup successfully: {{filename}}", "cleanup": "Cleaned up {{count}} expired backup files"}, "reset": {"success": "Configuration reset successfully: {{configType}}", "failed": "Configuration reset failed: {{configType}}, error: {{error}}"}}, "db": {"connection": {"success": "Database connected successfully: {{database}}", "failed": "Database connection failed: {{database}}, error: {{error}}", "closed": "Database connection closed: {{database}}", "timeout": "Database connection timeout: {{database}}"}, "query": {"success": "Database query successful, duration: {{duration}}ms", "failed": "Database query failed: {{error}}", "slow": "Database query slow, duration: {{duration}}ms", "empty": "Database query returned empty result"}, "migration": {"started": "Database migration started: {{version}}", "completed": "Database migration completed: {{version}}", "failed": "Database migration failed: {{error}}"}, "backup": {"started": "Database backup started", "completed": "Database backup completed: {{filename}}", "failed": "Database backup failed: {{error}}"}}, "file": {"add": {"success": "File added successfully: {{filename}}", "failed": "File add failed: {{filename}}, error: {{error}}", "duplicate": "File already exists: {{filename}}", "invalid": "Invalid file path: {{filename}}"}, "remove": {"success": "File removed successfully: {{filename}}", "failed": "File removal failed: {{filename}}, error: {{error}}", "notFound": "File not found: {{filename}}"}, "launch": {"success": "File launched successfully: {{filename}}, duration: {{duration}}ms", "failed": "File launch failed: {{filename}}, error: {{error}}", "adminRequired": "File requires admin privileges to launch: {{filename}}", "timeout": "File launch timeout: {{filename}}"}, "scan": {"started": "File scan started: {{directory}}", "completed": "File scan completed, found {{count}} files", "failed": "File scan failed: {{error}}"}}, "ui": {"component": {"mounted": "Component mounted: {{component}}", "unmounted": "Component unmounted: {{component}}", "rendered": "Component rendered: {{component}}, duration: {{duration}}ms", "error": "Component render error: {{component}}, error: {{error}}"}, "theme": {"changed": "Theme changed successfully: {{theme}}", "failed": "Theme change failed: {{error}}", "loaded": "Theme loaded successfully: {{theme}}", "reset": "Theme reset to default"}, "navigation": {"changed": "Page navigation: {{from}} -> {{to}}", "failed": "Page navigation failed: {{error}}"}}, "i18n": {"language": {"changed": "Language changed successfully: {{from}} -> {{to}}", "failed": "Language change failed: {{error}}", "detected": "System language detected: {{language}}", "fallback": "Using fallback language: {{language}}", "loading_saved": "Loading saved language: {{language}}", "load_failed": "Failed to load saved language, using default: {{error}}"}, "translation": {"loaded": "Translation file loaded successfully: {{language}}", "failed": "Translation file load failed: {{language}}, error: {{error}}", "missing": "Missing translation key: {{key}}", "cached": "Translation cached: {{language}}", "file_missing": "Missing translation file: {{filePath}}", "file_load_failed": "Failed to load translation file {{language}}/{{namespace}}: {{error}}", "resources_reload_failed": "Failed to reload translation resources: {{error}}"}}, "ipc": {"message": {"sent": "IPC message sent: {{channel}}", "received": "IPC message received: {{channel}}", "failed": "IPC message handling failed: {{channel}}, error: {{error}}", "timeout": "IPC message timeout: {{channel}}"}, "handler": {"registered": "IPC handler registered: {{channel}}", "unregistered": "IPC handler unregistered: {{channel}}", "error": "IPC handler error: {{channel}}, error: {{error}}", "registering": "Registering IPC handlers...", "all_registered": "All IPC handlers registered"}}, "perf": {"startup": {"time": "Application startup time: {{duration}}ms", "slow": "Application startup slow: {{duration}}ms"}, "memory": {"usage": "Memory usage: {{used}}MB / {{total}}MB ({{percentage}}%)", "high": "High memory usage: {{used}}MB", "gc": "Garbage collection completed, freed: {{freed}}MB"}, "cpu": {"usage": "CPU usage: {{percentage}}%", "high": "High CPU usage: {{percentage}}%"}, "operation": {"completed": "Operation completed: {{operation}}, duration: {{duration}}ms", "slow": "Operation slow: {{operation}}, duration: {{duration}}ms"}}, "theme": {"load": {"success": "Theme loaded successfully: {{theme}}", "failed": "Theme load failed: {{theme}}, error: {{error}}"}, "apply": {"success": "Theme applied successfully: {{theme}}", "failed": "Theme apply failed: {{theme}}, error: {{error}}"}, "export": {"success": "Theme exported successfully: {{filename}}", "failed": "Theme export failed: {{error}}"}, "import": {"success": "Theme imported successfully: {{filename}}", "failed": "Theme import failed: {{filename}}, error: {{error}}"}}, "backup": {"create": {"success": "Backup created successfully: {{filename}}", "failed": "Backup creation failed: {{error}}", "started": "Starting backup creation..."}, "restore": {"success": "Backup restored successfully: {{filename}}", "failed": "Backup restore failed: {{filename}}, error: {{error}}", "started": "Starting backup restore: {{filename}}"}, "cleanup": {"success": "Backup cleanup completed, deleted {{count}} files", "failed": "Backup cleanup failed: {{error}}"}}, "system": {"error": {"uncaught": "Uncaught exception: {{error}}", "unhandled": "Unhandled promise rejection: {{reason}}", "critical": "System critical error: {{error}}", "translation_failed": "Failed to translate log message: {{key}}", "log_manager_internal": "LogManager internal error: {{error}}", "write_log_failed": "Failed to write log: {{error}}", "flush_buffer_failed": "Failed to flush buffer for {{filename}}: {{error}}"}, "resource": {"low_memory": "Low system memory: {{available}}MB", "low_disk": "Low disk space: {{available}}GB", "high_cpu": "High CPU usage: {{usage}}%"}, "info": {"renderer_loaded": "Renderer script loaded", "electron_api_available": "ElectronAPI available: {{available}}", "react_app_rendered": "React app rendered successfully", "react_app_failed": "Failed to render React app: {{error}}", "app_mounted": "App component mounted", "waiting_electron_api": "Waiting for ElectronAPI...", "electron_api_ready": "ElectronAPI is ready", "app_initialization_completed": "App initialization completed", "main_i18n_initialized": "MainI18n initialized with language: {{language}}", "main_i18n_failed": "MainI18n failed to initialize: {{error}}", "main_i18n_not_initialized": "MainI18n not initialized, returning default value", "main_i18n_resources_reloaded": "MainI18n resources reloaded", "main_i18n_reload_failed": "MainI18n failed to reload resources: {{error}}", "log_config_updated": "Log config updated"}}, "logs_panel": {"error": {"load_failed": "Failed to load logs", "export_failed": "Failed to export logs"}, "info": {"system_ready": "Log system is ready", "logs_loaded": "Loaded {{count}} log entries", "export_success": "Logs exported successfully"}, "actions": {"refresh": "Refresh Logs", "export": "Export Logs"}, "ui": {"recent_logs": "Recent Logs"}}}
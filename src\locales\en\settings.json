{"title": "Application Settings", "tabs": {"general": "General", "userPreferences": "User Preferences", "appearance": "Appearance", "behavior": "Behavior", "advanced": "Advanced", "about": "About", "language": "Language", "updates": "Updates", "privacy": "Privacy", "security": "Security", "performance": "Performance", "config": "Configuration Management"}, "general": {"title": "General Settings", "language": "Interface Language", "autoStart": "Start on Boot", "minimizeToTray": "Minimize to System Tray", "closeToTray": "Close to System Tray", "showSplashScreen": "Show Splash Screen", "checkUpdatesOnStart": "Check Updates on Start", "enableNotifications": "Enable Notifications", "soundEnabled": "Enable Sound", "confirmExit": "Confirm on Exit"}, "appearance": {"title": "Appearance Settings", "theme": "Theme", "colorScheme": "Color Scheme", "fontSize": "Font Size", "fontFamily": "Font Family", "windowOpacity": "Window Opacity", "showMenuBar": "Show Menu Bar", "showStatusBar": "Show Status Bar", "showToolbar": "Show Toolbar", "compactMode": "Compact Mode", "animationsEnabled": "Enable Animations"}, "behavior": {"title": "Behavior Settings", "defaultAction": "Default Action", "doubleClickAction": "Double Click Action", "middleClickAction": "Middle Click Action", "dragDropAction": "Drag & Drop Action", "autoSave": "Auto Save", "autoSaveInterval": "Auto Save Interval", "backupEnabled": "Enable Backup", "maxBackups": "<PERSON>", "rememberWindowState": "Remember Window State", "restoreLastSession": "Restore Last Session"}, "advanced": {"title": "Advanced Settings", "hardwareAcceleration": "Hardware Acceleration", "debugMode": "Debug Mode", "verboseLogging": "Verbose Logging", "maxLogSize": "<PERSON>g Si<PERSON>", "cacheSize": "<PERSON><PERSON>", "clearCache": "<PERSON>ache", "resetSettings": "Reset Settings", "exportSettings": "Export Settings", "importSettings": "Import Settings", "factoryReset": "Factory Reset"}, "language": {"title": "Language Settings", "interface": "Interface Language", "current": "Current Language", "autoDetect": "Auto Detect", "followSystem": "Follow System", "availableLanguages": "Available Languages", "downloadLanguagePack": "Download Language Pack", "languagePackStatus": "Language Pack Status", "restartRequired": "<PERSON><PERSON> Required", "autoDetectDesc": "Automatically select interface language based on system language", "fallback": "Fallback Language", "supported": "Supported Languages", "formats": "Format Settings", "dateFormat": "Date Format", "timeFormat": "Time Format", "numberFormat": "Number Format", "decimal": "Decimal Point", "thousands": "Thousands Separator", "currency": "<PERSON><PERSON><PERSON><PERSON>"}, "updates": {"title": "Update Settings", "autoUpdate": "Auto Update", "checkForUpdates": "Check for Updates", "updateChannel": "Update Channel", "stable": "Stable", "beta": "Beta", "dev": "Development", "downloadInBackground": "Download in Background", "installOnRestart": "Install on Restart", "currentVersion": "Current Version", "latestVersion": "Latest Version", "updateAvailable": "Update Available", "upToDate": "Up to Date"}, "privacy": {"title": "Privacy Settings", "analytics": "Usage Analytics", "crashReports": "Crash Reports", "errorReporting": "Error Reporting", "usageStatistics": "Usage Statistics", "dataCollection": "Data Collection", "anonymousData": "Anonymous Data", "clearData": "Clear Data", "dataLocation": "Data Location", "exportData": "Export Data", "deleteData": "Delete Data"}, "security": {"title": "Security Settings", "autoLock": "Auto Lock", "lockTimeout": "Lock Timeout", "requirePassword": "Require Password", "encryptData": "Encrypt Data", "secureConnection": "Secure Connection", "certificateValidation": "Certificate Validation", "trustedSources": "Trusted Sources", "blockUnsafeContent": "Block Unsafe Content", "sandboxMode": "Sandbox Mode", "permissionManagement": "Permission Management"}, "performance": {"title": "Performance Optimization", "hardwareAcceleration": "Hardware Acceleration", "hardwareAccelerationDesc": "Enable GPU hardware acceleration for better performance", "cacheSize": "<PERSON><PERSON> (MB)", "cacheSizeDesc": "Icon and thumbnail cache size", "enableVirtualization": "Enable Virtualization", "enableVirtualizationDesc": "Use virtual scrolling for large number of files"}, "actions": {"save": "Save Settings", "cancel": "Cancel", "apply": "Apply", "reset": "Reset", "restore": "Rest<PERSON>", "export": "Export", "import": "Import", "backup": "Backup", "clear": "Clear", "refresh": "Refresh"}, "messages": {"settingsSaved": "Settings Saved", "settingsReset": "Settings Reset", "settingsExported": "Settings Exported", "settingsImported": "Settings Imported", "restartRequired": "Restart required to apply changes", "confirmReset": "Confirm reset all settings?", "confirmClearCache": "Confirm clear cache?", "confirmFactoryReset": "Confirm factory reset? This will delete all data!", "invalidSettingsFile": "Invalid settings file", "settingsCorrupted": "Settings file corrupted, using default settings", "settingUpdated": "Setting updated", "settingUpdateFailed": "Setting update failed", "settingUpdateError": "Error occurred while updating setting", "languageChanged": "Language changed", "loadingSettings": "Loading settings...", "loadSettingsFailed": "Failed to load settings", "cannotLoadSettings": "Cannot load settings", "checkConfigSystem": "Please check if the configuration system is working properly"}, "window": {"title": "Window Settings", "maximizeOnStart": "Maximize on Start", "maximizeOnStartDesc": "Automatically maximize window when application starts", "alwaysOnTop": "Always on Top", "alwaysOnTopDesc": "Keep window always in front"}, "startup": {"title": "Startup Settings", "autoLaunch": "Auto Launch", "autoLaunchDesc": "Automatically run QuickStart when system starts", "minimizeToTray": "Minimize to Tray", "minimizeToTrayDesc": "Minimize to system tray when window is closed", "showSplashScreen": "Show Splash Screen", "showSplashScreenDesc": "Show welcome screen when application starts", "checkUpdates": "Check Updates", "checkUpdatesDesc": "Check for application updates on startup"}, "userPreferences": {"title": "User Preferences", "description": "Personalization settings and preference configuration", "global": {"title": "Global Preferences"}, "fileList": {"title": "File List Preferences", "showSize": "Show Size", "showSizeDesc": "Display file size in file list", "showModifiedTime": "Show Modified Time", "showModifiedTimeDesc": "Display last modified time in file list", "showAddedTime": "Show Added Time", "showAddedTimeDesc": "Display time when file was added to list", "showLaunchCount": "Show Launch Count", "showLaunchCountDesc": "Display file launch count in file list"}, "confirmBeforeDelete": "Confirm Before Delete", "confirmBeforeDeleteDesc": "Show confirmation dialog before deleting files", "confirmBeforeExit": "Confirm Before Exit", "confirmBeforeExitDesc": "Show confirmation dialog before exiting application", "rememberWindowState": "Remember Window State", "rememberWindowStateDesc": "Remember window size and position", "enableNotifications": "Enable Notifications", "enableNotificationsDesc": "Show system notifications", "enableDragDrop": "Enable Drag & Drop", "enableDragDropDesc": "Support dragging files to application", "showFileExtensions": "Show File Extensions", "showFileExtensionsDesc": "Show extensions in file list"}}
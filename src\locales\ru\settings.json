{"title": "Настройки приложения", "tabs": {"general": "Общие", "userPreferences": "Пользовательские предпочтения", "appearance": "Вн<PERSON><PERSON>ний вид", "behavior": "Поведение", "advanced": "Дополнительно", "about": "О программе", "language": "Язык", "updates": "Обновления", "privacy": "Конфиденциальность", "security": "Безопасность", "performance": "Производительность", "config": "Управление конфигурацией"}, "general": {"title": "Общие настройки", "language": "Язык интерфейса", "autoStart": "Автозапуск", "minimizeToTray": "Свернуть в трей", "closeToTray": "Закрыть в трей", "showSplashScreen": "Показать заставку", "checkUpdatesOnStart": "Проверять обновления при запуске", "enableNotifications": "Включить уведомления", "soundEnabled": "Включить звук", "confirmExit": "Подтверждать выход"}, "appearance": {"title": "Настройки внешнего вида", "theme": "Тема", "colorScheme": "Цветовая схема", "fontSize": "Размер шрифта", "fontFamily": "Семейство шрифтов", "windowOpacity": "Прозрачность окна", "showMenuBar": "Показать панель меню", "showStatusBar": "Показать строку состояния", "showToolbar": "Показать панель инструментов", "compactMode": "Компактный режим", "animationsEnabled": "Включить анимации"}, "behavior": {"title": "Настройки поведения", "doubleClickAction": "Действие двойного клика", "singleClickAction": "Действие одного клика", "middleClickAction": "Действие среднего клика", "contextMenuEnabled": "Включить контекстное меню", "keyboardShortcuts": "Горячие клавиши", "mouseGestures": "Жесты мыши", "autoSave": "Автосохранение", "backupOnExit": "Резервное копирование при выходе", "confirmActions": "Подтверждать действия", "undoLevels": "Уровни отмены"}, "advanced": {"title": "Дополнительные настройки", "debugMode": "Режим отладки", "verboseLogging": "Подробное логирование", "experimentalFeatures": "Экспериментальные функции", "developerMode": "Режим разработчика", "customCSS": "Пользовательский CSS", "pluginSupport": "Поддержка плагинов", "apiAccess": "Доступ к API", "advancedSearch": "Расширенный поиск", "indexing": "Индексирование", "caching": "Кэширование"}, "language": {"title": "Настройки языка", "interface": "Язык интерфейса", "current": "Текущий язык", "autoDetect": "Автоопределение", "followSystem": "Следовать системе", "availableLanguages": "Доступные языки", "downloadLanguagePack": "Скачать языковой пакет", "languagePackStatus": "Статус языкового пакета", "restartRequired": "Требуется перезапуск", "autoDetectDesc": "Автоматически выбирать язык интерфейса на основе системного языка", "fallback": "Резервный язык", "supported": "Поддерживаемые языки", "formats": "Настройки формата", "dateFormat": "Формат даты", "timeFormat": "Формат времени", "numberFormat": "Формат чисел", "decimal": "Десятичная точка", "thousands": "Разделитель тысяч", "currency": "Валюта"}, "updates": {"title": "Настройки обновлений", "autoUpdate": "Автообновление", "checkForUpdates": "Проверить обновления", "updateChannel": "Канал обновлений", "stable": "Стабильная", "beta": "Бета", "dev": "Разработка", "downloadInBackground": "Скачивать в фоне", "installOnRestart": "Установить при перезапуске", "currentVersion": "Текущая версия", "latestVersion": "Последняя версия", "updateAvailable": "Доступно обновление", "upToDate": "Актуальная версия"}, "privacy": {"title": "Настройки конфиденциальности", "analytics": "Аналитика использования", "crashReports": "Отчеты о сбоях", "errorReporting": "Отчеты об ошибках", "usageStatistics": "Статистика использования", "dataCollection": "Сбор данных", "anonymousData": "Анонимные данные", "clearData": "Очистить данные", "dataLocation": "Расположение данных", "exportData": "Экспорт данных", "deleteData": "Удалить данные"}, "security": {"title": "Настройки безопасности", "autoLock": "Автоблокировка", "lockTimeout": "Таймаут блокировки", "requirePassword": "Требовать пароль", "encryptData": "Шифровать данные", "secureConnection": "Безопасное соединение", "certificateValidation": "Проверка сертификатов", "trustedSources": "Доверенные источники", "blockUnsafeContent": "Блокировать небезопасный контент", "sandboxMode": "Режим песочницы", "permissionManagement": "Управление разрешениями"}, "performance": {"title": "Оптимизация производительности", "hardwareAcceleration": "Аппаратное ускорение", "hardwareAccelerationDesc": "Включить аппаратное ускорение GPU для лучшей производительности", "cacheSize": "Размер кэша (МБ)", "cacheSizeDesc": "Размер кэша иконок и миниатюр", "enableVirtualization": "Включить виртуализацию", "enableVirtualizationDesc": "Использовать виртуальную прокрутку для большого количества файлов"}, "actions": {"save": "Сохранить настройки", "cancel": "Отмена", "apply": "Применить", "reset": "Сброс", "restore": "Восстановить", "export": "Экспорт", "import": "Импорт", "backup": "Резервная копия", "clear": "Очистить", "refresh": "Обновить"}, "messages": {"settingsSaved": "Настройки сохранены", "settingsReset": "Настройки сброшены", "settingsExported": "Настройки экспортированы", "settingsImported": "Настройки импортированы", "restartRequired": "Требуется перезапуск для применения изменений", "confirmReset": "Подтвердить сброс всех настроек?", "confirmClearCache": "Подтвердить очистку кэша?", "confirmFactoryReset": "Подтвердить сброс к заводским настройкам? Это удалит все данные!", "invalidSettingsFile": "Неверный файл настроек", "settingsCorrupted": "Файл настроек поврежден, используются настройки по умолчанию", "settingUpdated": "Настройка обновлена", "settingUpdateFailed": "Не удалось обновить настройку", "settingUpdateError": "Произошла ошибка при обновлении настройки", "languageChanged": "Язык изменен", "loadingSettings": "Загрузка настроек...", "loadSettingsFailed": "Не удалось загрузить настройки", "cannotLoadSettings": "Невозможно загрузить настройки", "checkConfigSystem": "Пожалуйста, проверьте, работает ли система конфигурации правильно"}, "window": {"title": "Настройки окна", "maximizeOnStart": "Развернуть при запуске", "maximizeOnStartDesc": "Автоматически разворачивать окно при запуске приложения", "alwaysOnTop": "Поверх всех окон", "alwaysOnTopDesc": "Держать окно всегда поверх других"}, "startup": {"title": "Настройки запуска", "autoLaunch": "Автозапуск", "autoLaunchDesc": "Автоматически запускать QuickStart при старте системы", "minimizeToTray": "Свернуть в трей", "minimizeToTrayDesc": "Сворачивать в системный трей при закрытии окна", "showSplashScreen": "Показать заставку", "showSplashScreenDesc": "Показывать экран приветствия при запуске приложения", "checkUpdates": "Проверять обновления", "checkUpdatesDesc": "Проверять обновления приложения при запуске"}, "userPreferences": {"title": "Пользовательские предпочтения", "description": "Персонализация настроек и конфигурация предпочтений", "global": {"title": "Глобальные предпочтения"}, "fileList": {"title": "Предпочтения списка файлов", "showSize": "Показывать размер", "showSizeDesc": "Отображать размер файла в списке файлов", "showModifiedTime": "Показывать время изменения", "showModifiedTimeDesc": "Отображать время последнего изменения в списке файлов", "showAddedTime": "Показывать время добавления", "showAddedTimeDesc": "Отображать время добавления файла в список", "showLaunchCount": "Показывать количество запусков", "showLaunchCountDesc": "Отображать количество запусков файла в списке"}, "confirmBeforeDelete": "Подтверждать удаление", "confirmBeforeDeleteDesc": "Показывать диалог подтверждения перед удалением файлов", "confirmBeforeExit": "Подтверждать выход", "confirmBeforeExitDesc": "Показывать диалог подтверждения перед выходом из приложения", "rememberWindowState": "Запомнить состояние окна", "rememberWindowStateDesc": "Запомнить размер и положение окна", "enableNotifications": "Включить уведомления", "enableNotificationsDesc": "Показывать системные уведомления", "enableDragDrop": "Включить перетаскивание", "enableDragDropDesc": "Поддержка перетаскивания файлов в приложение", "showFileExtensions": "Показывать расширения файлов", "showFileExtensionsDesc": "Показывать расширения в списке файлов"}}
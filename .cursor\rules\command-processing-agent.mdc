---
description: 当开发者输入"/"开头的指令时使用
globs: 
alwaysApply: false
---
# 指令处理核心规范

## 使用场景
- 用户输入以`/`开头的指令时
- 需要统一指令处理逻辑时
- 指令解析和分发时

## 关键规则
- 准确识别和解析用户指令
- 根据指令类型调用对应的处理规则
- 确保所有指令处理都更新相关文档
- 按优先级处理多个指令
- 提供指令执行状态反馈

## 支持的指令类型
- `/检查` - 代码质量检查 (check-commands-agent)
- `/开发` - 功能开发 (development-commands-agent)
- `/测试` - 测试执行 (testing-commands-agent)
- `/问题` - 问题解决 (problem-solving-agent)
- `/继续` - 任务恢复 (continue-commands-agent)
- `/状态` - 项目状态 (project-status-detection-agent)
- `/讨论` - 模块剖析和调研 (discussion-commands-agent)

## 指令处理流程
1. **指令识别** → 2. **参数解析** → 3. **规则匹配** → 4. **任务执行** → 5. **状态更新** → 6. **结果反馈**
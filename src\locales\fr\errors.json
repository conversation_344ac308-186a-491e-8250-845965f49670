{"general": {"unknown": "<PERSON><PERSON><PERSON> inconnue", "networkError": "<PERSON><PERSON><PERSON>", "serverError": "<PERSON><PERSON><PERSON> serveur", "clientError": "Erreur client", "timeout": "<PERSON><PERSON><PERSON>'attente d<PERSON><PERSON>", "cancelled": "Opération annulée", "forbidden": "Accès interdit", "unauthorized": "Accès non autorisé", "notFound": "Ressource non trouvée", "conflict": "Conflit de ressource", "tooManyRequests": "Trop de requêtes", "internalError": "E<PERSON>ur interne", "serviceUnavailable": "Service indisponible", "badGateway": "Passerelle défa<PERSON>nte", "gatewayTimeout": "<PERSON><PERSON><PERSON>'attente de la passerelle"}, "file": {"notFound": "Fichier non trouvé", "accessDenied": "Accès au fichier refusé", "alreadyExists": "Le fichier existe déjà", "tooLarge": "Fichier trop volumineux", "invalidFormat": "Format de fichier invalide", "corrupted": "<PERSON><PERSON><PERSON> corro<PERSON>", "readError": "<PERSON><PERSON><PERSON> de lecture du fi<PERSON>er", "writeError": "Erreur d'écriture du fichier", "deleteError": "Erreur de suppression du fichier", "copyError": "Erreur de copie du fichier", "moveError": "<PERSON><PERSON><PERSON> de déplacement du fichier", "renameError": "<PERSON><PERSON>ur de renommage du fichier", "permissionDenied": "Permissions de fichier insuffisantes", "diskFull": "Espace disque plein", "pathTooLong": "<PERSON>em<PERSON> de fichier trop long", "invalidPath": "<PERSON><PERSON><PERSON> de <PERSON><PERSON> invalide", "locked": "<PERSON><PERSON><PERSON>", "inUse": "Fichier en cours d'utilisation"}, "config": {"loadFailed": "Échec du chargement de la configuration", "saveFailed": "Échec de la sauvegarde de la configuration", "parseFailed": "Échec de l'analyse de la configuration", "validationFailed": "Échec de la validation de la configuration", "corrupted": "Fichier de configuration corrompu", "notFound": "Fichier de configuration non trouvé", "accessDenied": "Accès au fichier de configuration refusé", "backupFailed": "Échec de la sauvegarde de la configuration", "restoreFailed": "Échec de la restauration de la configuration", "migrationFailed": "Échec de la migration de la configuration", "versionMismatch": "Version de configuration incompatible", "schemaError": "<PERSON><PERSON><PERSON> de schéma de configuration", "defaultsError": "Erreur de configuration par défaut"}, "theme": {"loadFailed": "Échec du chargement du thème", "saveFailed": "Échec de la sauvegarde du thème", "parseFailed": "Échec de l'analyse du thème", "invalidFormat": "Format de thème invalide", "notFound": "Thème non trouvé", "alreadyExists": "Le thème existe déjà", "exportFailed": "Échec de l'exportation du thème", "importFailed": "Échec de l'importation du thème", "applyFailed": "Échec de l'application du thème", "resetFailed": "Échec de la réinitialisation du thème", "colorInvalid": "<PERSON><PERSON> de couleur invalide", "fontNotFound": "Police non trouvée", "effectNotSupported": "Effet non pris en charge"}, "app": {"initFailed": "Échec de l'initialisation de l'application", "startupError": "<PERSON><PERSON><PERSON> <PERSON>", "shutdownError": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "updateFailed": "Échec de la mise à jour", "installFailed": "Échec de l'installation", "uninstallFailed": "Échec de la désinstallation", "migrationFailed": "Échec de la migration des données", "backupFailed": "Échec de la sauvegarde", "restoreFailed": "Échec de la restauration", "syncFailed": "Échec de la synchronisation", "authFailed": "Échec de l'authentification", "licenseInvalid": "Licence invalide", "versionIncompatible": "Version incompatible"}, "ipc": {"channelNotFound": "Canal IPC non trouvé", "handlerNotFound": "Gestionnaire IPC non trouvé", "messageInvalid": "Message IPC invalide", "timeoutError": "<PERSON><PERSON><PERSON> de d<PERSON><PERSON> d'attente IPC", "connectionLost": "Connexion IPC perdue", "permissionDenied": "Permission IPC refusée", "serializationError": "Erreur de sérialisation IPC", "deserializationError": "Erreur de désérialisation IPC", "protocolError": "Erreur de protocole IPC", "bufferOverflow": "Débordement de tampon IPC"}, "database": {"connectionFailed": "Échec de la connexion à la base de données", "queryFailed": "Échec de la requête de base de données", "transactionFailed": "Échec de la transaction de base de données", "migrationFailed": "Échec de la migration de base de données", "backupFailed": "Échec de la sauvegarde de base de données", "restoreFailed": "Échec de la restauration de base de données", "corruptedData": "Données de base de données corrompues", "schemaError": "E<PERSON>ur de schéma de base de données", "constraintViolation": "Violation de contrainte de base de données", "deadlock": "Interblocage de base de données", "timeout": "<PERSON><PERSON><PERSON> d'attente de base de données", "diskFull": "Disque de base de données plein", "permissionDenied": "Permission de base de données refusée", "versionMismatch": "Version de base de données incompatible"}, "network": {"connectionFailed": "Échec de la connexion réseau", "requestFailed": "Échec de la requête réseau", "responseInvalid": "Réponse réseau invalide", "timeout": "<PERSON><PERSON><PERSON>'attente r<PERSON>", "offline": "<PERSON><PERSON><PERSON> hors ligne", "dnsError": "Erreur de résolution DNS", "sslError": "Erreur SSL/TLS", "proxyError": "<PERSON><PERSON>ur de proxy", "rateLimited": "<PERSON><PERSON>", "hostUnreachable": "Hôte inaccessible", "portBlocked": "Port bloqué", "protocolError": "<PERSON><PERSON>ur de protocole"}, "ui": {"renderFailed": "Échec du rendu de l'interface", "componentError": "<PERSON><PERSON><PERSON> de composant", "eventHandlerError": "Erreur de gestionnaire d'événements", "stateUpdateError": "Erreur de mise à jour d'état", "routingError": "<PERSON><PERSON><PERSON> de routage", "validationError": "Erreur de validation de formulaire", "loadingError": "Erreur de chargement", "resourceNotFound": "Ressource d'interface non trouvée"}, "actions": {"retry": "<PERSON><PERSON><PERSON><PERSON>", "contact": "<PERSON>er le support", "reload": "Recharger", "restart": "Redémarrer l'app", "reset": "Réinitialiser", "restore": "<PERSON><PERSON><PERSON>"}}
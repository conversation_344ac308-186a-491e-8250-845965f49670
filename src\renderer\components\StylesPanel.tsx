import React from 'react';
import {
  Card,
  Row,
  Col,
  Typography,
  ColorPicker,
  Slider,
  Switch,
  Select,
  Space,
  Button,
  Divider,
  message,
  Tabs,
  Spin,
  Alert
} from 'antd';
import {
  BgColorsOutlined,
  FontSizeOutlined,
  BorderOutlined,
  EyeOutlined,
  DownloadOutlined,
  UploadOutlined,
  ReloadOutlined,
  PictureOutlined,
  SettingOutlined,
  SkinOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { useLayoutConfig } from '../hooks/useConfig';
import { BackgroundSettingsPanel } from './BackgroundSettingsPanel';
import type { Color } from 'antd/es/color-picker';

const { Title, Text } = Typography;
const { Option } = Select;

export const StylesPanel: React.FC = () => {
  const { t } = useTranslation(['theme', 'common']);
  const { themeConfig, updateTheme, resetTheme, toggleMode } = useTheme();
  const { config: layoutConfig, updateConfig: updateLayoutConfig, loading: layoutLoading } = useLayoutConfig();

  // 字体选项 - 从翻译文件获取
  const fontOptions = [
    { label: t('theme:fonts.options.appleSystem'), value: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif' },
    { label: t('theme:fonts.options.microsoftYaHei'), value: '"Microsoft YaHei", sans-serif' },
    { label: t('theme:fonts.options.pingFangSC'), value: '"PingFang SC", sans-serif' },
    { label: t('theme:fonts.options.helveticaNeue'), value: '"Helvetica Neue", Arial, sans-serif' },
    { label: t('theme:fonts.options.sfProDisplay'), value: '"SF Pro Display", sans-serif' },
    { label: t('theme:fonts.options.segoeUI'), value: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif' },
  ];

  // 处理颜色变化
  const handleColorChange = (colorKey: string, color: Color) => {
    const hexColor = color.toHexString();
    if (colorKey === 'primary') {
      updateTheme({ primaryColor: hexColor });
    } else {
      updateTheme({
        customColors: {
          ...themeConfig.customColors,
          [colorKey]: hexColor,
        },
      });
    }
  };

  // 导出主题配置
  const handleExportTheme = () => {
    const themeData = JSON.stringify(themeConfig, null, 2);
    const blob = new Blob([themeData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'quickstart-theme.json';
    a.click();
    URL.revokeObjectURL(url);
    message.success(t('theme:messages.exportSuccess', '主题配置已导出'));
  };

  // 导入主题配置
  const handleImportTheme = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const themeData = JSON.parse(e.target?.result as string);
            updateTheme(themeData);
            message.success('主题配置已导入');
          } catch {
            message.error('主题配置文件格式错误');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  // 处理布局设置变更
  const handleLayoutChange = async (settingPath: string, value: unknown) => {
    try {
      if (!layoutConfig) {
        console.error('Layout config not loaded');
        return;
      }

      const pathParts = settingPath.split('.');
      const updateObj: Record<string, unknown> = {};

      if (pathParts.length === 1) {
        updateObj[pathParts[0]] = value;
      } else if (pathParts.length === 2) {
        const [category, property] = pathParts;
        const currentCategory = (layoutConfig as unknown as Record<string, unknown>)[category];
        updateObj[category] = {
          ...(typeof currentCategory === 'object' && currentCategory !== null ? currentCategory : {}),
          [property]: value
        };
      }

      console.log('Updating layout setting:', settingPath, '=', value);
      const success = await updateLayoutConfig(updateObj);

      if (success) {
        message.success(t('theme:messages.layoutUpdated'));
      } else {
        message.error(t('theme:messages.layoutUpdateFailed'));
      }
    } catch (error) {
      console.error('Failed to update layout setting:', error);
      message.error(t('theme:messages.layoutUpdateError'));
    }
  };

  // 主题预设
  const themePresets = [
    {
      name: t('theme:presets.iosBlue', 'iOS 蓝色'),
      config: {
        primaryColor: '#007AFF',
        glassEffect: true,
        borderRadius: 12,
        customColors: {
          surface: '#f2f2f7',
          text: '#000000',
          textSecondary: '#8e8e93',
          border: '#c6c6c8',
          shadow: 'rgba(0, 0, 0, 0.1)',
        },
      },
    },
    {
      name: t('theme:presets.macosGreen', 'macOS 绿色'),
      config: {
        primaryColor: '#30D158',
        glassEffect: true,
        borderRadius: 8,
        customColors: {
          surface: '#f5f5f7',
          text: '#1d1d1f',
          textSecondary: '#86868b',
          border: '#d2d2d7',
          shadow: 'rgba(0, 0, 0, 0.08)',
        },
      },
    },
    {
      name: t('theme:presets.appleOrange', 'Apple 橙色'),
      config: {
        primaryColor: '#FF9500',
        glassEffect: true,
        borderRadius: 10,
        customColors: {
          surface: '#fafafa',
          text: '#1d1d1f',
          textSecondary: '#86868b',
          border: '#d2d2d7',
          shadow: 'rgba(255, 149, 0, 0.1)',
        },
      },
    },
    {
      name: t('theme:presets.deepSpaceGray', '深空灰'),
      config: {
        mode: 'dark' as const,
        primaryColor: '#0A84FF',
        glassEffect: true,
        borderRadius: 12,
        customColors: {
          surface: '#1c1c1e',
          text: '#ffffff',
          textSecondary: '#8e8e93',
          border: '#38383a',
          shadow: 'rgba(0, 0, 0, 0.5)',
        },
      },
    },
    {
      name: t('theme:presets.midnightBlue', '午夜蓝'),
      config: {
        mode: 'dark' as const,
        primaryColor: '#64D2FF',
        glassEffect: true,
        borderRadius: 8,
        customColors: {
          surface: '#2c2c2e',
          text: '#ffffff',
          textSecondary: '#a1a1a6',
          border: '#48484a',
          shadow: 'rgba(0, 0, 0, 0.6)',
        },
      },
    },
  ];

  const tabItems = [
    {
      key: 'global',
      label: t('theme:global.title', '全局主题'),
      icon: <SettingOutlined />,
      children: (
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 主色调设置 */}
          <Card size="small" title={t('theme:global.primaryColor', '主色调')}>
            <Row gutter={16} align="middle">
              <Col span={6}>
                <Text>{t('theme:global.primaryColor', '主要颜色')}:</Text>
              </Col>
              <Col span={6}>
                <ColorPicker
                  value={themeConfig.primaryColor}
                  onChange={(color) => handleColorChange('primary', color)}
                  showText
                  trigger="click"
                />
              </Col>
              <Col span={12}>
                <Text type="secondary">{t('theme:global.primaryColorDesc', '影响按钮、链接等主要元素的颜色')}</Text>
              </Col>
            </Row>
          </Card>

          {/* 明暗模式切换 */}
          <Card size="small" title={t('theme:global.mode', '显示模式')}>
            <Row gutter={16} align="middle">
              <Col span={6}>
                <Text>{t('theme:global.darkMode', '暗黑模式')}:</Text>
              </Col>
              <Col span={6}>
                <Switch
                  checked={themeConfig.mode === 'dark'}
                  onChange={toggleMode}
                />
              </Col>
              <Col span={12}>
                <Text type="secondary">{t('theme:global.modeDesc', '切换明亮和暗黑显示模式')}</Text>
              </Col>
            </Row>
          </Card>

          {/* 字体设置 */}
          <Card size="small" title={t('theme:global.font', '字体设置')}>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <Row gutter={16} align="middle">
                <Col span={6}>
                  <Text>{t('theme:global.fontSize', '字体大小')}:</Text>
                </Col>
                <Col span={12}>
                  <Slider
                    min={12}
                    max={18}
                    value={themeConfig.fontSize}
                    onChange={(value) => updateTheme({ fontSize: value })}
                    marks={{
                      12: '12px',
                      14: '14px',
                      16: '16px',
                      18: '18px',
                    }}
                  />
                </Col>
                <Col span={6}>
                  <Text type="secondary">{themeConfig.fontSize}px</Text>
                </Col>
              </Row>

              <Row gutter={16} align="middle">
                <Col span={6}>
                  <Text>{t('theme:global.borderRadius', '圆角大小')}:</Text>
                </Col>
                <Col span={12}>
                  <Slider
                    min={0}
                    max={16}
                    value={themeConfig.borderRadius}
                    onChange={(value) => updateTheme({ borderRadius: value })}
                    marks={{
                      0: '0px',
                      4: '4px',
                      8: '8px',
                      12: '12px',
                      16: '16px',
                    }}
                  />
                </Col>
                <Col span={6}>
                  <Text type="secondary">{themeConfig.borderRadius}px</Text>
                </Col>
              </Row>
            </Space>
          </Card>

          {/* 主题预设 */}
          <Card size="small" title={t('theme:ui.themePresets')}>
            <Space wrap>
              {themePresets.map((preset) => (
                <Button
                  key={preset.name}
                  onClick={() => updateTheme(preset.config)}
                  style={{
                    background: preset.config.primaryColor,
                    color: 'white',
                    border: 'none',
                  }}
                >
                  {preset.name}
                </Button>
              ))}
            </Space>
          </Card>
        </Space>
      ),
    },
    {
      key: 'colors',
      label: t('theme:colors.title', '界面颜色'),
      icon: <SkinOutlined />,
      children: (
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 界面颜色设置 */}
          <Card size="small" title={t('theme:colors.interface', '界面颜色')}>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              {Object.entries(themeConfig.customColors).map(([key, value]) => (
                <Row key={key} gutter={16} align="middle">
                  <Col span={8}>
                    <Text>{t(`theme:colors.labels.${key}`, key)}:</Text>
                  </Col>
                  <Col span={8}>
                    <ColorPicker
                      value={value}
                      onChange={(color) => handleColorChange(key, color)}
                      showText
                      trigger="click"
                    />
                  </Col>
                  <Col span={8}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {t(`theme:colors.descriptions.${key}`, '')}
                    </Text>
                  </Col>
                </Row>
              ))}
            </Space>
          </Card>
        </Space>
      ),
    },
    {
      key: 'effects',
      label: t('theme:effects.title'),
      icon: <EyeOutlined />,
      children: (
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Card size="small" title={t('theme:ui.interfaceEffects')}>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('theme:effects.glassEffect')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={themeConfig.glassEffect}
                    onChange={(checked) => updateTheme({ glassEffect: checked })}
                  />
                </Col>
              </Row>

              {themeConfig.glassEffect && (
                <Row gutter={16} align="middle">
                  <Col span={8}>
                    <Text>{t('theme:effects.transparency')}:</Text>
                  </Col>
                  <Col span={12}>
                    <Slider
                      min={0.1}
                      max={1}
                      step={0.1}
                      value={themeConfig.glassOpacity}
                      onChange={(value) => updateTheme({ glassOpacity: value })}
                    />
                  </Col>
                  <Col span={4}>
                    <Text>{Math.round(themeConfig.glassOpacity * 100)}%</Text>
                  </Col>
                </Row>
              )}

              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('theme:effects.borderRadius')}:</Text>
                </Col>
                <Col span={12}>
                  <Slider
                    min={0}
                    max={20}
                    value={themeConfig.borderRadius}
                    onChange={(value) => updateTheme({ borderRadius: value })}
                  />
                </Col>
                <Col span={4}>
                  <Text>{themeConfig.borderRadius}px</Text>
                </Col>
              </Row>

              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Text>{t('theme:effects.compactMode')}:</Text>
                </Col>
                <Col span={16}>
                  <Switch
                    checked={themeConfig.compactMode}
                    onChange={(checked) => updateTheme({ compactMode: checked })}
                  />
                </Col>
              </Row>
            </Space>
          </Card>
        </Space>
      ),
    },
    {
      key: 'layout',
      label: t('theme:layout.title'),
      icon: <BorderOutlined />,
      children: (
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {layoutLoading ? (
            <div style={{ textAlign: 'center', padding: '50px 0' }}>
              <Spin size="large" />
              <div style={{ marginTop: 16 }}>
                <Text type="secondary">{t('theme:messages.loadingLayout')}</Text>
              </div>
            </div>
          ) : !layoutConfig ? (
            <Alert
              message={t('theme:messages.noLayoutConfig')}
              description={t('theme:messages.checkConfigSystem')}
              type="warning"
              showIcon
            />
          ) : (
            <>
              {/* 侧边栏设置 */}
              <Card size="small" title={t('theme:ui.sidebar.title')}>
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <Row gutter={16} align="middle">
                    <Col span={8}>
                      <Text>{t('theme:ui.sidebar.position')}:</Text>
                    </Col>
                    <Col span={16}>
                      <Select
                        value={layoutConfig.sidebar?.position || 'left'}
                        onChange={(value) => handleLayoutChange('sidebar.position', value)}
                        style={{ width: '100%' }}
                      >
                        <Option value="left">{t('theme:ui.sidebar.positions.left')}</Option>
                        <Option value="right">{t('theme:ui.sidebar.positions.right')}</Option>
                      </Select>
                    </Col>
                  </Row>

                  <Row gutter={16} align="middle">
                    <Col span={8}>
                      <Text>{t('theme:ui.sidebar.collapsed')}:</Text>
                    </Col>
                    <Col span={16}>
                      <Switch
                        checked={layoutConfig.sidebar?.collapsed || false}
                        onChange={(checked) => handleLayoutChange('sidebar.collapsed', checked)}
                      />
                    </Col>
                  </Row>

                  <Row gutter={16} align="middle">
                    <Col span={8}>
                      <Text>{t('theme:ui.sidebar.autoHide')}:</Text>
                    </Col>
                    <Col span={16}>
                      <Switch
                        checked={layoutConfig.sidebar?.autoHide || false}
                        onChange={(checked) => handleLayoutChange('sidebar.autoHide', checked)}
                      />
                    </Col>
                  </Row>
                </Space>
              </Card>



              {/* 状态栏设置 */}
              <Card size="small" title={t('theme:ui.statusBar.title')}>
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <Row gutter={16} align="middle">
                    <Col span={8}>
                      <Text>{t('theme:ui.statusBar.visible')}:</Text>
                    </Col>
                    <Col span={16}>
                      <Switch
                        checked={layoutConfig.statusBar?.visible || false}
                        onChange={(checked) => handleLayoutChange('statusBar.visible', checked)}
                      />
                    </Col>
                  </Row>

                  <Row gutter={16} align="middle">
                    <Col span={8}>
                      <Text>{t('theme:ui.statusBar.showFileCount')}:</Text>
                    </Col>
                    <Col span={16}>
                      <Switch
                        checked={layoutConfig.statusBar?.showFileCount || false}
                        onChange={(checked) => handleLayoutChange('statusBar.showFileCount', checked)}
                      />
                    </Col>
                  </Row>

                  <Row gutter={16} align="middle">
                    <Col span={8}>
                      <Text>{t('theme:ui.statusBar.showPath')}:</Text>
                    </Col>
                    <Col span={16}>
                      <Switch
                        checked={layoutConfig.statusBar?.showPath || false}
                        onChange={(checked) => handleLayoutChange('statusBar.showPath', checked)}
                      />
                    </Col>
                  </Row>
                </Space>
              </Card>
            </>
          )}
        </Space>
      ),
    },
    {
      key: 'background',
      label: t('theme:background.title'),
      icon: <PictureOutlined />,
      children: <BackgroundSettingsPanel />,
    },
  ];

  return (
    <div className="styles-panel-container page-container">
      <Card
        className="apple-card"
        style={{
          background: themeConfig.glassEffect
            ? 'var(--glass-background)'
            : 'rgba(255, 255, 255, 0.8)', // 半透明背景，不完全遮挡
          backdropFilter: themeConfig.glassEffect
            ? 'var(--glass-backdrop-filter)'
            : 'blur(10px)',
        }}
      >
        <div style={{ marginBottom: 16 }}>
          <Title level={3} style={{ margin: 0 }}>
            {t('theme:ui.title')}
          </Title>
          <Text type="secondary">
            {t('theme:ui.description')}
          </Text>
        </div>

        {/* 操作按钮 */}
        <Space style={{ marginBottom: 16 }}>
          <Button
            icon={<DownloadOutlined />}
            onClick={handleExportTheme}
            className="apple-button"
          >
            {t('theme:actions.export')}
          </Button>
          <Button
            icon={<UploadOutlined />}
            onClick={handleImportTheme}
            className="apple-button"
          >
            {t('theme:actions.import')}
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={resetTheme}
            className="apple-button"
          >
            {t('theme:actions.reset')}
          </Button>
        </Space>

        <Divider />

        {/* 主要设置区域 */}
        <Tabs
          items={tabItems}
          size="small"
          style={{ minHeight: 400 }}
        />
      </Card>
    </div>
  );
};

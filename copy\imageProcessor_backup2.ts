/**
 * 图片处理工具类
 * 提供图片压缩、格式转换、尺寸调整等功能
 */

export interface ImageMetadata {
  width: number;
  height: number;
  format: string;
  size: number;
  aspectRatio: number;
  originalPath?: string; // 可选字段：原始文件路径或名称
}

export interface ProcessImageOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number; // 0-1
  format?: 'jpeg' | 'png' | 'webp';
  maintainAspectRatio?: boolean;
}

export interface ProcessedImage {
  blob: Blob;
  dataUrl: string;
  metadata: ImageMetadata;
}

/**
 * 图片处理器类
 */
export class ImageProcessor {
  /**
   * 验证图片文件
   */
  static validateImageFile(file: File): { valid: boolean; error?: string } {
    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: `不支持的图片格式: ${file.type}。支持的格式: JPEG, PNG, GIF, WebP`
      };
    }

    // 检查文件大小 (最大50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `图片文件过大: ${(file.size / 1024 / 1024).toFixed(2)}MB。最大支持: 50MB`
      };
    }

    return { valid: true };
  }

  /**
   * 读取图片文件并获取元数据
   */
  static async getImageMetadata(file: File): Promise<ImageMetadata> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const url = URL.createObjectURL(file);

      img.onload = () => {
        URL.revokeObjectURL(url);
        
        const metadata: ImageMetadata = {
          width: img.naturalWidth,
          height: img.naturalHeight,
          format: file.type.split('/')[1],
          size: file.size,
          aspectRatio: img.naturalWidth / img.naturalHeight,
        };

        resolve(metadata);
      };

      img.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('无法读取图片文件'));
      };

      img.src = url;
    });
  }

  /**
   * 处理图片（压缩、调整尺寸等）
   */
  static async processImage(file: File, options: ProcessImageOptions = {}): Promise<ProcessedImage> {
    const {
      maxWidth = 4096,
      maxHeight = 4096,
      quality = 0.85,
      format = 'jpeg',
      maintainAspectRatio = true,
    } = options;

    return new Promise((resolve, reject) => {
      const img = new Image();
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('无法创建Canvas上下文'));
        return;
      }

      img.onload = () => {
        let { width, height } = img;

        // 计算新尺寸
        if (maintainAspectRatio) {
          const aspectRatio = width / height;
          
          if (width > maxWidth) {
            width = maxWidth;
            height = width / aspectRatio;
          }
          
          if (height > maxHeight) {
            height = maxHeight;
            width = height * aspectRatio;
          }
        } else {
          width = Math.min(width, maxWidth);
          height = Math.min(height, maxHeight);
        }

        // 设置Canvas尺寸
        canvas.width = width;
        canvas.height = height;

        // 绘制图片
        ctx.drawImage(img, 0, 0, width, height);

        // 转换为Blob
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('图片处理失败'));
              return;
            }

            // 创建DataURL用于预览
            const reader = new FileReader();
            reader.onload = () => {
              const dataUrl = reader.result as string;
              
              const metadata: ImageMetadata = {
                width,
                height,
                format,
                size: blob.size,
                aspectRatio: width / height,
              };

              resolve({
                blob,
                dataUrl,
                metadata,
              });
            };

            reader.onerror = () => {
              reject(new Error('无法生成图片预览'));
            };

            reader.readAsDataURL(blob);
          },
          `image/${format}`,
          quality
        );
      };

      img.onerror = () => {
        reject(new Error('无法加载图片'));
      };

      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * 创建图片缩略图
   */
  static async createThumbnail(file: File, size: number = 200): Promise<string> {
    const processed = await this.processImage(file, {
      maxWidth: size,
      maxHeight: size,
      quality: 0.7,
      format: 'jpeg',
      maintainAspectRatio: true,
    });

    return processed.dataUrl;
  }

  /**
   * 批量处理图片
   */
  static async processImages(
    files: File[], 
    options: ProcessImageOptions = {},
    onProgress?: (index: number, total: number) => void
  ): Promise<ProcessedImage[]> {
    const results: ProcessedImage[] = [];

    for (let i = 0; i < files.length; i++) {
      try {
        const processed = await this.processImage(files[i], options);
        results.push(processed);
        
        if (onProgress) {
          onProgress(i + 1, files.length);
        }
      } catch (error) {
        console.error(`处理图片 ${files[i].name} 失败:`, error);
        // 继续处理其他图片
      }
    }

    return results;
  }

  /**
   * 检查图片是否需要压缩
   */
  static shouldCompress(metadata: ImageMetadata, maxSize: number = 2 * 1024 * 1024): boolean {
    return metadata.size > maxSize || metadata.width > 2048 || metadata.height > 2048;
  }

  /**
   * 获取推荐的压缩设置
   */
  static getRecommendedCompression(metadata: ImageMetadata): ProcessImageOptions {
    const { width, height, size } = metadata;
    
    // 根据图片大小和尺寸推荐压缩设置
    if (size > 10 * 1024 * 1024) { // 大于10MB
      return {
        maxWidth: 1920,
        maxHeight: 1080,
        quality: 0.7,
        format: 'jpeg',
      };
    } else if (size > 5 * 1024 * 1024) { // 大于5MB
      return {
        maxWidth: 2560,
        maxHeight: 1440,
        quality: 0.8,
        format: 'jpeg',
      };
    } else if (width > 4096 || height > 4096) { // 超高分辨率
      return {
        maxWidth: 3840,
        maxHeight: 2160,
        quality: 0.85,
        format: 'jpeg',
      };
    } else {
      return {
        quality: 0.9,
        format: metadata.format === 'png' ? 'png' : 'jpeg',
      };
    }
  }

  /**
   * 从URL加载图片
   */
  static async loadImageFromUrl(url: string, timeout: number = 30000): Promise<ImageMetadata> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      let isResolved = false;
      
      // 设置超时
      const timeoutId = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          reject(new Error(`网络图片加载超时：${timeout / 1000}秒`));
        }
      }, timeout);
      
      img.onload = () => {
        if (isResolved) return;
        isResolved = true;
        clearTimeout(timeoutId);
        
        const metadata: ImageMetadata = {
          width: img.naturalWidth,
          height: img.naturalHeight,
          format: this.getFormatFromUrl(url),
          size: 0, // URL图片无法获取确切大小
          aspectRatio: img.naturalWidth / img.naturalHeight,
        };

        resolve(metadata);
      };

      img.onerror = () => {
        if (isResolved) return;
        isResolved = true;
        clearTimeout(timeoutId);
        reject(new Error('无法加载网络图片：图片可能不存在、网络错误或CORS限制'));
      };

      // 设置跨域属性
      img.crossOrigin = 'anonymous';
      img.src = url;
    });
  }

  /**
   * 从URL下载图片并转换为File对象
   */
  static async downloadImageFromUrl(url: string, timeout: number = 30000): Promise<File> {
    try {
      console.log('ImageProcessor: Starting image download from URL:', url);
      
      // 创建带超时的fetch请求
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        signal: controller.signal,
        mode: 'cors',
        headers: {
          'Accept': 'image/*',
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 验证内容类型
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.startsWith('image/')) {
        throw new Error(`无效的内容类型: ${contentType}。期望的是图片类型。`);
      }

      // 检查文件大小（如果服务器提供）
      const contentLength = response.headers.get('content-length');
      if (contentLength) {
        const size = parseInt(contentLength, 10);
        const maxSize = 50 * 1024 * 1024; // 50MB
        if (size > maxSize) {
          throw new Error(`图片文件过大: ${Math.round(size / 1024 / 1024)}MB。最大支持: 50MB`);
        }
      }

      const blob = await response.blob();
      
      // 获取文件名
      const filename = this.getFilenameFromUrl(url) || `downloaded-image-${Date.now()}.${this.getFormatFromUrl(url)}`;
      
      // 转换为File对象
      const file = new File([blob], filename, { type: blob.type });
      
      console.log('ImageProcessor: Image downloaded successfully:', {
        url,
        filename,
        size: file.size,
        type: file.type
      });
      
      return file;
    } catch (error) {
      console.error('ImageProcessor: Failed to download image from URL:', error);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error(`网络图片下载超时：${timeout / 1000}秒`);
        } else if (error.message.includes('CORS')) {
          throw new Error('CORS错误：无法访问此图片，服务器不允许跨域请求');
        } else if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
          throw new Error('网络错误：请检查网络连接和图片链接是否有效');
        }
      }
      
      throw new Error(`下载网络图片失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 从URL获取文件格式
   */
  private static getFormatFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname.toLowerCase();
      
      if (pathname.endsWith('.jpg') || pathname.endsWith('.jpeg')) return 'jpeg';
      if (pathname.endsWith('.png')) return 'png';
      if (pathname.endsWith('.gif')) return 'gif';
      if (pathname.endsWith('.webp')) return 'webp';
      
      return 'jpeg'; // 默认格式
    } catch {
      return 'jpeg';
    }
  }

  /**
   * 从URL获取文件名
   */
  private static getFilenameFromUrl(url: string): string | null {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const filename = pathname.split('/').pop();
      return filename && filename.includes('.') ? filename : null;
    } catch {
      return null;
    }
  }

  /**
   * 验证图片URL
   */
  static validateImageUrl(url: string): { valid: boolean; error?: string } {
    try {
      const urlObj = new URL(url);
      
      // 检查协议
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return {
          valid: false,
          error: '只支持HTTP和HTTPS协议的图片链接'
        };
      }

      // 检查文件扩展名
      const pathname = urlObj.pathname.toLowerCase();
      const supportedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
      const hasValidExtension = supportedExtensions.some(ext => pathname.endsWith(ext));
      
      if (!hasValidExtension) {
        return {
          valid: false,
          error: '图片链接必须以支持的格式结尾 (.jpg, .jpeg, .png, .gif, .webp)'
        };
      }

      return { valid: true };
    } catch (error) {
      return {
        valid: false,
        error: '无效的图片链接格式'
      };
    }
  }
}

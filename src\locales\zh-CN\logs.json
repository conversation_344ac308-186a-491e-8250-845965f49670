{"app": {"startup": {"success": "应用程序启动成功，版本: {{version}}", "failed": "应用程序启动失败: {{error}}", "ready": "应用程序已就绪", "initializing": "正在初始化应用程序...", "shutdown": "应用程序正在关闭", "crashed": "应用程序崩溃: {{error}}"}, "environment": {"header": "=== 环境配置信息 ===", "footer": "===================", "node_env": "NODE_ENV: {{env}}", "is_packaged": "app.isPackaged: {{packaged}}", "is_dev": "isDev (computed): {{dev}}", "platform": "process.platform: {{platform}}", "arch": "process.arch: {{arch}}", "renderer_url": "RENDERER_URL: {{url}}", "dirname": "__dirname: {{dirname}}"}, "window": {"created": "窗口创建成功: {{windowType}}", "failed": "窗口创建失败: {{error}}", "closed": "主窗口已关闭", "minimized": "窗口已最小化", "maximized": "窗口已最大化", "restored": "窗口已还原", "creating": "正在创建主窗口...", "loading_url": "正在加载渲染器URL: {{url}}", "shown": "主窗口已显示", "renderer_loaded": "渲染进程加载完成"}, "tray": {"created": "系统托盘创建成功", "failed": "系统托盘创建失败: {{error}}", "clicked": "系统托盘被点击"}, "database": {"initialized": "数据库管理器已初始化"}}, "config": {"load": {"success": "配置加载成功: {{configType}}", "failed": "配置加载失败: {{configType}}, 错误: {{error}}", "notFound": "配置文件不存在: {{configType}}，使用默认配置", "corrupted": "配置文件损坏: {{configType}}，已重置为默认配置"}, "save": {"success": "配置保存成功: {{configType}}", "failed": "配置保存失败: {{configType}}, 错误: {{error}}"}, "backup": {"created": "配置备份创建成功: {{filename}}", "failed": "配置备份创建失败: {{error}}", "restored": "配置从备份恢复成功: {{filename}}", "cleanup": "清理了 {{count}} 个过期备份文件"}, "reset": {"success": "配置重置成功: {{configType}}", "failed": "配置重置失败: {{configType}}, 错误: {{error}}"}}, "db": {"connection": {"success": "数据库连接成功: {{database}}", "failed": "数据库连接失败: {{database}}, 错误: {{error}}", "closed": "数据库连接已关闭: {{database}}", "timeout": "数据库连接超时: {{database}}"}, "query": {"success": "数据库查询成功，耗时: {{duration}}ms", "failed": "数据库查询失败: {{error}}", "slow": "数据库查询较慢，耗时: {{duration}}ms", "empty": "数据库查询结果为空"}, "migration": {"started": "数据库迁移开始: {{version}}", "completed": "数据库迁移完成: {{version}}", "failed": "数据库迁移失败: {{error}}"}, "backup": {"started": "数据库备份开始", "completed": "数据库备份完成: {{filename}}", "failed": "数据库备份失败: {{error}}"}}, "file": {"add": {"success": "文件添加成功: {{filename}}", "failed": "文件添加失败: {{filename}}, 错误: {{error}}", "duplicate": "文件已存在: {{filename}}", "invalid": "无效的文件路径: {{filename}}"}, "remove": {"success": "文件删除成功: {{filename}}", "failed": "文件删除失败: {{filename}}, 错误: {{error}}", "notFound": "文件不存在: {{filename}}"}, "launch": {"success": "文件启动成功: {{filename}}，耗时: {{duration}}ms", "failed": "文件启动失败: {{filename}}, 错误: {{error}}", "adminRequired": "文件需要管理员权限启动: {{filename}}", "timeout": "文件启动超时: {{filename}}"}, "scan": {"started": "文件扫描开始: {{directory}}", "completed": "文件扫描完成，发现 {{count}} 个文件", "failed": "文件扫描失败: {{error}}"}}, "i18n": {"language": {"changed": "语言切换成功: {{from}} -> {{to}}", "failed": "语言切换失败: {{error}}", "detected": "检测到系统语言: {{language}}", "fallback": "使用回退语言: {{language}}", "loading_saved": "正在加载保存的语言: {{language}}", "load_failed": "加载保存的语言失败，使用默认语言: {{error}}"}, "translation": {"loaded": "翻译文件加载成功: {{language}}", "failed": "翻译文件加载失败: {{language}}, 错误: {{error}}", "missing": "缺少翻译键: {{key}}", "cached": "翻译已缓存: {{language}}", "file_missing": "翻译文件缺失: {{filePath}}", "file_load_failed": "翻译文件加载失败: {{language}}/{{namespace}}: {{error}}", "resources_reload_failed": "翻译资源重新加载失败: {{error}}"}}, "ipc": {"message": {"sent": "IPC消息发送: {{channel}}", "received": "IPC消息接收: {{channel}}", "failed": "IPC消息处理失败: {{channel}}, 错误: {{error}}", "timeout": "IPC消息超时: {{channel}}"}, "handler": {"registered": "IPC处理器注册: {{channel}}", "unregistered": "IPC处理器注销: {{channel}}", "error": "IPC处理器错误: {{channel}}, 错误: {{error}}", "registering": "正在注册IPC处理器...", "all_registered": "所有IPC处理器已注册"}}, "perf": {"startup": {"time": "应用启动时间: {{duration}}ms", "slow": "应用启动较慢: {{duration}}ms"}, "memory": {"usage": "内存使用: {{used}}MB / {{total}}MB ({{percentage}}%)", "high": "内存使用过高: {{used}}MB", "gc": "垃圾回收完成，释放: {{freed}}MB"}, "cpu": {"usage": "CPU使用率: {{percentage}}%", "high": "CPU使用率过高: {{percentage}}%"}, "operation": {"completed": "操作完成: {{operation}}，耗时: {{duration}}ms", "slow": "操作较慢: {{operation}}，耗时: {{duration}}ms"}}, "theme": {"load": {"success": "主题加载成功: {{theme}}", "failed": "主题加载失败: {{theme}}, 错误: {{error}}"}, "apply": {"success": "主题应用成功: {{theme}}", "failed": "主题应用失败: {{theme}}, 错误: {{error}}"}, "export": {"success": "主题导出成功: {{filename}}", "failed": "主题导出失败: {{error}}"}, "import": {"success": "主题导入成功: {{filename}}", "failed": "主题导入失败: {{filename}}, 错误: {{error}}"}}, "backup": {"create": {"success": "备份创建成功: {{filename}}", "failed": "备份创建失败: {{error}}", "started": "开始创建备份..."}, "restore": {"success": "备份恢复成功: {{filename}}", "failed": "备份恢复失败: {{filename}}, 错误: {{error}}", "started": "开始恢复备份: {{filename}}"}, "cleanup": {"success": "备份清理完成，删除了 {{count}} 个文件", "failed": "备份清理失败: {{error}}"}}, "system": {"error": {"uncaught": "未捕获的异常: {{error}}", "unhandled": "未处理的Promise拒绝: {{reason}}", "critical": "系统关键错误: {{error}}", "translation_failed": "翻译日志消息失败: {{key}}", "log_manager_internal": "日志管理器内部错误: {{error}}", "write_log_failed": "写入日志失败: {{error}}", "flush_buffer_failed": "刷新缓冲区失败 {{filename}}: {{error}}"}, "resource": {"low_memory": "系统内存不足: {{available}}MB", "low_disk": "磁盘空间不足: {{available}}GB", "high_cpu": "CPU使用率过高: {{usage}}%"}, "info": {"renderer_loaded": "渲染器脚本已加载", "electron_api_available": "ElectronAPI可用: {{available}}", "react_app_rendered": "React应用渲染成功", "react_app_failed": "React应用渲染失败: {{error}}", "app_mounted": "应用组件已挂载", "waiting_electron_api": "等待ElectronAPI...", "electron_api_ready": "ElectronAPI已就绪", "app_initialization_completed": "应用初始化完成", "main_i18n_initialized": "主进程i18n已初始化，语言: {{language}}", "main_i18n_failed": "主进程i18n初始化失败: {{error}}", "main_i18n_not_initialized": "主进程i18n未初始化，返回默认值", "main_i18n_resources_reloaded": "主进程i18n资源已重新加载", "main_i18n_reload_failed": "主进程i18n资源重新加载失败: {{error}}", "log_config_updated": "日志配置已更新"}}, "ui": {"component": {"mounted": "组件挂载完成: {{component}}", "unmounted": "组件卸载完成: {{component}}", "rendered": "组件渲染完成: {{component}}，耗时: {{duration}}ms", "error": "组件渲染错误: {{component}}, 错误: {{error}}"}, "theme": {"changed": "主题切换成功: {{theme}}", "failed": "主题切换失败: {{error}}", "loaded": "主题加载成功: {{theme}}", "reset": "主题重置为默认"}, "navigation": {"changed": "页面导航: {{from}} -> {{to}}", "failed": "页面导航失败: {{error}}"}, "logs_panel": {"error": {"load_failed": "日志加载失败", "export_failed": "日志导出失败"}, "info": {"system_ready": "日志系统已就绪", "logs_loaded": "已加载 {{count}} 条日志记录", "export_success": "日志导出成功"}, "actions": {"refresh": "刷新日志", "export": "导出日志"}, "ui": {"recent_logs": "最近日志"}}}}
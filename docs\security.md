# QuickStart 安全配置文档

## 安全架构概述

QuickStart 采用了多层安全防护策略，确保应用的安全性和用户数据的保护。

## 1. Electron 安全配置

### 1.1 上下文隔离 (Context Isolation)
```javascript
webPreferences: {
  contextIsolation: true,  // 启用上下文隔离
  sandbox: true,           // 启用沙箱模式
  nodeIntegration: false,  // 禁用 Node.js 集成
  webSecurity: true        // 启用 Web 安全
}
```

**作用：**
- 防止渲染进程直接访问 Node.js API
- 隔离主世界和隔离世界的 JavaScript 上下文
- 防止恶意脚本注入和 XSS 攻击

### 1.2 沙箱模式 (Sandbox)
```javascript
webPreferences: {
  sandbox: true,
  allowRunningInsecureContent: false,
  experimentalFeatures: false
}
```

**作用：**
- 限制渲染进程的系统访问权限
- 防止恶意代码执行系统命令
- 提供额外的安全隔离层

### 1.3 预加载脚本安全
```javascript
// preload.js 中的安全检查
window.addEventListener('DOMContentLoaded', () => {
  // 移除可能的 Node.js 全局变量
  delete window.require;
  delete window.exports;
  delete window.module;
  delete window.process;
  delete window.global;
  delete window.Buffer;
});
```

**作用：**
- 清理可能泄露的 Node.js API
- 确保渲染进程环境的纯净性
- 防止意外的 API 暴露

## 2. 内容安全策略 (CSP)

### 2.1 HTML 中的 CSP 配置
```html
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline';
  style-src 'self' 'unsafe-inline';
  img-src 'self' data: file: blob:;
  font-src 'self' data:;
  connect-src 'self';
  media-src 'self';
  object-src 'none';
  child-src 'none';
  worker-src 'none';
  frame-src 'none';
  base-uri 'self';
  form-action 'self';
">
```

**策略说明：**
- `default-src 'self'`: 默认只允许同源资源
- `script-src 'self' 'unsafe-inline'`: 允许内联脚本（开发需要）
- `img-src 'self' data: file: blob:`: 允许本地、Data URL、文件和Blob URL图片
  - `blob:`: 支持图片处理功能中的临时Blob对象
- `object-src 'none'`: 禁止 Flash、Java 等插件
- `frame-src 'none'`: 禁止嵌入框架

### 2.2 开发服务器 CSP
```javascript
// webpack.renderer.config.js
devServer: {
  headers: {
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"
  }
}
```

## 3. IPC 通信安全

### 3.1 白名单 API 暴露
```javascript
// preload.js 中只暴露必要的 API
contextBridge.exposeInMainWorld('electronAPI', {
  // 严格限制暴露的方法
  readConfig: (configName) => ipcRenderer.invoke('file:readConfig', configName),
  writeConfig: (configName, data) => ipcRenderer.invoke('file:writeConfig', configName, data),
  // ... 其他安全的 API
});
```

**安全原则：**
- 最小权限原则：只暴露必要的功能
- 参数验证：对所有输入进行验证
- 错误处理：安全地处理异常情况

### 3.2 IPC 消息验证
```javascript
// 主进程中的参数验证示例
ipcMain.handle('file:readConfig', async (event, configName) => {
  // 验证配置文件名
  if (typeof configName !== 'string' || !/^[a-zA-Z0-9-_]+$/.test(configName)) {
    throw new Error('无效的配置文件名');
  }
  
  // 防止路径遍历攻击
  const safePath = path.join(APP_DATA_PATH, 'config', `${configName}.json`);
  if (!safePath.startsWith(path.join(APP_DATA_PATH, 'config'))) {
    throw new Error('无效的文件路径');
  }
  
  // ... 执行操作
});
```

## 4. 文件系统安全

### 4.1 路径验证
```javascript
// 防止路径遍历攻击
function validatePath(userPath, allowedBasePath) {
  const resolvedPath = path.resolve(userPath);
  const resolvedBasePath = path.resolve(allowedBasePath);
  
  if (!resolvedPath.startsWith(resolvedBasePath)) {
    throw new Error('路径不在允许的范围内');
  }
  
  return resolvedPath;
}
```

### 4.2 配置文件保护
```javascript
// 配置文件权限设置
const configPath = path.join(APP_DATA_PATH, 'config', `${configName}.json`);

// 确保目录存在且权限正确
if (!fs.existsSync(path.dirname(configPath))) {
  fs.mkdirSync(path.dirname(configPath), { 
    recursive: true, 
    mode: 0o700  // 只有所有者可读写执行
  });
}

// 写入文件时设置权限
fs.writeFileSync(configPath, data, { 
  encoding: 'utf8', 
  mode: 0o600  // 只有所有者可读写
});
```

## 5. 外部链接安全

### 5.1 链接验证
```javascript
// 安全的外部链接打开
ipcMain.handle('system:openExternal', async (event, url) => {
  try {
    // 验证 URL 格式
    const urlObj = new URL(url);
    
    // 只允许 HTTP 和 HTTPS 协议
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      throw new Error('不支持的协议');
    }
    
    await shell.openExternal(url);
    return true;
  } catch (error) {
    console.error('打开外部链接失败:', error);
    return false;
  }
});
```

### 5.2 窗口打开控制
```javascript
// 主进程中控制新窗口创建
mainWindow.webContents.setWindowOpenHandler(({ url }) => {
  // 所有外部链接都通过系统浏览器打开
  shell.openExternal(url);
  return { action: 'deny' };
});
```

## 6. 数据保护

### 6.1 敏感数据加密
```javascript
// 对敏感配置进行加密存储（如果需要）
const crypto = require('crypto');

function encryptData(data, key) {
  const cipher = crypto.createCipher('aes-256-cbc', key);
  let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
}

function decryptData(encryptedData, key) {
  const decipher = crypto.createDecipher('aes-256-cbc', key);
  let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return JSON.parse(decrypted);
}
```

### 6.2 日志安全
```javascript
// 安全的错误日志记录
function logError(error, context = {}) {
  // 过滤敏感信息
  const sanitizedContext = Object.keys(context).reduce((acc, key) => {
    if (!['password', 'token', 'key', 'secret'].some(sensitive => 
      key.toLowerCase().includes(sensitive))) {
      acc[key] = context[key];
    }
    return acc;
  }, {});
  
  const logEntry = {
    timestamp: new Date().toISOString(),
    error: error.message,
    stack: error.stack,
    context: sanitizedContext
  };
  
  // 写入日志文件
  fs.appendFileSync(errorLogPath, JSON.stringify(logEntry) + '\n');
}
```

## 7. 更新安全

### 7.1 自动更新验证
```javascript
// 使用 electron-updater 进行安全更新
const { autoUpdater } = require('electron-updater');

// 配置更新服务器和签名验证
autoUpdater.setFeedURL({
  provider: 'github',
  owner: 'your-org',
  repo: 'quickstart',
  private: false
});

// 验证更新包签名
autoUpdater.checkForUpdatesAndNotify();
```

## 8. 安全检查清单

### 8.1 开发阶段
- [ ] 启用上下文隔离
- [ ] 禁用 Node.js 集成
- [ ] 启用沙箱模式
- [ ] 配置 CSP 策略
- [ ] 限制 IPC API 暴露
- [ ] 验证所有用户输入
- [ ] 实现路径遍历保护

### 8.2 生产阶段
- [ ] 移除开发工具访问
- [ ] 启用代码签名
- [ ] 配置自动更新
- [ ] 实施日志监控
- [ ] 定期安全审计
- [ ] 用户权限最小化

## 9. 安全最佳实践

1. **定期更新依赖**：保持 Electron 和其他依赖的最新版本
2. **代码审查**：对所有安全相关代码进行审查
3. **渗透测试**：定期进行安全测试
4. **用户教育**：提供安全使用指南
5. **事件响应**：建立安全事件响应流程

## 10. 已知风险和缓解措施

### 10.1 文件访问风险
**风险**：用户可能添加恶意文件到启动列表
**缓解**：
- 文件类型验证
- 病毒扫描集成（可选）
- 用户确认机制

### 10.2 配置文件篡改
**风险**：恶意软件可能修改配置文件
**缓解**：
- 配置文件完整性检查
- 备份和恢复机制
- 权限控制

### 10.3 网络请求风险
**风险**：应用可能被用于网络攻击
**缓解**：
- 限制网络访问
- URL 白名单
- 请求频率限制

---

**注意**：安全是一个持续的过程，需要定期评估和更新安全措施。
